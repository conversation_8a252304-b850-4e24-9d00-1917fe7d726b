{
    "acl_command": {
        "command": "/app/connected_home_ip/out/chip-tool-linux/chip-tool accesscontrol write acl [{\"fabricIndex\": 1, \"privilege\": 5, \"authMode\": 2, \"subjects\": null, \"targets\": null}, {\"fabricIndex\": 1, \"privilege\": 3, \"authMode\": 2, \"subjects\": [31], \"targets\": null}] 32 0",
        "returncode": 0,
        "stderr": "",
        "stdout":"[0;34m[1750764174.733] [155:155] [DL] ChipLinuxStorage::Init: Using KVS config file: /tmp/chip_tool_kvs[0m\n[1;31m[1750764174.733] [155:155] [DL] ChipLinuxStorage::Init: Attempt to re-initialize with KVS config file: /tmp/chip_kvs[0m\n[0;34m[1750764174.735] [155:155] [DL] ChipLinuxStorage::Init: Using KVS config file: /tmp/chip_factory.ini[0m\n[0;34m[1750764174.735] [155:155] [DL] ChipLinuxStorage::Init: Using KVS config file: /tmp/chip_config.ini[0m\n[0;34m[1750764174.735] [155:155] [DL] ChipLinuxStorage::Init: Using KVS config file: /tmp/chip_counters.ini[0m\n[0;34m[1750764174.744] [155:155] [DL] Wrote settings to /tmp/chip_counters.ini[0m\n[0;32m[1750764174.744] [155:155] [DL] NVS set: chip-counters/reboot-count = 37 (0x25)[0m\n[0;32m[1750764174.745] [155:155] [DL] Got Ethernet interface: end0[0m\n[0;32m[1750764174.745] [155:155] [DL] Found the primary Ethernet interface:end0[0m\n[0;32m[1750764174.745] [155:155] [DL] Got WiFi interface: wlan0[0m\n[1;31m[1750764174.745] [155:155] [DL] Failed to reset WiFi statistic counts[0m\n[0;32m[1750764174.745] [155:155] [PAF] WiFiPAF: WiFiPAFLayer::Init()[0m\n[0;34m[1750764174.745] [155:155] [IN] UDP::Init bind&listen port=0[0m\n[0;34m[1750764174.745] [155:155] [IN] UDP::Init bound to port=48148[0m\n[0;34m[1750764174.745] [155:155] [IN] UDP::Init bind&listen port=0[0m\n[0;34m[1750764174.745] [155:155] [IN] UDP::Init bound to port=45401[0m\n[0;34m[1750764174.745] [155:155] [IN] BLEBase::Init - setting/overriding transport[0m\n[0;34m[1750764174.745] [155:155] [IN] WiFiPAFBase::Init - setting/overriding transport[0m\n[0;34m[1750764174.745] [155:155] [IN] TransportMgr initialized[0m\n[0;34m[1750764174.745] [155:155] [FP] Initializing FabricTable from persistent storage[0m\n[0;32m[1750764174.745] [155:155] [TS] Last Known Good Time: 2023-10-14T01:16:48[0m\n[0;32m[1750764174.746] [155:155] [FP] Fabric index 0x1 was retrieved from storage. Compressed FabricId 0x7C3E96F4C2D76BB7, FabricId 0x0000000000000001, NodeId 0x000000000001B669, VendorId 0xFFF1[0m\n[0;32m[1750764174.746] [155:155] [DMG] Ember attribute persistence requires setting up[0m\n[0;32m[1750764174.746] [155:155] [ZCL] Using ZAP configuration...[0m\n[0;34m[1750764174.756] [155:155] [CTL] System State Initialized...[0m\n[0;32m[1750764174.756] [155:155] [CTL] Setting attestation nonce to random value[0m\n[0;32m[1750764174.756] [155:155] [CTL] Setting CSR nonce to random value[0m\n[0;34m[1750764174.756] [155:157] [DL] CHIP task running[0m\n[0;34m[1750764174.756] [155:157] [DL] HandlePlatformSpecificBLEEvent 32786[0m\n[0;32m[1750764174.756] [155:157] [CTL] Setting attestation nonce to random value[0m\n[0;32m[1750764174.756] [155:157] [CTL] Setting CSR nonce to random value[0m\n[0;32m[1750764174.756] [155:157] [CTL] Generating NOC[0m\n[0;32m[1750764174.757] [155:157] [FP] Validating NOC chain[0m\n[0;32m[1750764174.758] [155:157] [FP] NOC chain validation successful[0m\n[0;32m[1750764174.758] [155:157] [FP] Updated fabric at index: 0x1, Node ID: 0x000000000001B669[0m\n[0;32m[1750764174.758] [155:157] [TS] Last Known Good Time: 2023-10-14T01:16:48[0m\n[0;32m[1750764174.758] [155:157] [TS] New proposed Last Known Good Time: 2021-01-01T00:00:00[0m\n[0;32m[1750764174.758] [155:157] [TS] Retaining current Last Known Good Time[0m\n[0;32m[1750764174.758] [155:157] [FP] Metadata for Fabric 0x1 persisted to storage.[0m\n[0;32m[1750764174.759] [155:157] [TS] Committing Last Known Good Time to storage: 2023-10-14T01:16:48[0m\n[0;32m[1750764174.759] [155:157] [CTL] Joined the fabric at index 1. Fabric ID is 0x0000000000000001 (Compressed Fabric ID: 7C3E96F4C2D76BB7)[0m\n[0;32m[1750764174.761] [155:157] [TOO] Sending command to node 0x20[0m\n[0;34m[1750764174.762] [155:157] [CSM] FindOrEstablishSession: PeerId = [1:0000000000000020][0m\n[0;34m[1750764174.762] [155:157] [CSM] FindOrEstablishSession: No existing OperationalSessionSetup instance found[0m\n[0;34m[1750764174.762] [155:157] [DIS] OperationalSessionSetup[1:0000000000000020]: State change 1 --> 2[0m\n[0;32m[1750764174.763] [155:157] [DIS] Lookup started for 7C3E96F4C2D76BB7-0000000000000020[0m\n[0;34m[1750764174.764] [155:157] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764174.764] [155:157] [DIS] UDP:[fd89:8980:1890:1:439d:a208:448f:f16a%end0]:5540: new best score: 5 (for 7C3E96F4C2D76BB7-0000000000000020)[0m\n[0;32m[1750764174.764] [155:157] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-0000000000000020 after 3 ms[0m\n[0;32m[1750764174.764] [155:157] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764174.764] [155:157] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764174.764] [155:157] [DIS] UDP:[fd89:8980:1890:1:439d:a208:448f:f16a%end0]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-0000000000000020)[0m\n[0;32m[1750764174.764] [155:157] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-0000000000000020 after 3 ms[0m\n[0;32m[1750764174.764] [155:157] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764174.765] [155:157] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764174.765] [155:157] [DIS] UDP:[fd89:8980:1890:1:439d:a208:448f:f16a%hassio]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-0000000000000020)[0m\n[0;32m[1750764174.765] [155:157] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-0000000000000020 after 3 ms[0m\n[0;32m[1750764174.765] [155:157] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764174.765] [155:157] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764174.765] [155:157] [DIS] UDP:[fd89:8980:1890:1:439d:a208:448f:f16a%hassio]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-0000000000000020)[0m\n[0;32m[1750764174.765] [155:157] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-0000000000000020 after 4 ms[0m\n[0;32m[1750764174.765] [155:157] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764174.765] [155:157] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764174.765] [155:157] [DIS] UDP:[fd89:8980:1890:1:439d:a208:448f:f16a%docker0]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-0000000000000020)[0m\n[0;32m[1750764174.765] [155:157] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-0000000000000020 after 4 ms[0m\n[0;32m[1750764174.765] [155:157] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764174.765] [155:157] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764174.765] [155:157] [DIS] UDP:[fd89:8980:1890:1:439d:a208:448f:f16a%docker0]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-0000000000000020)[0m\n[0;32m[1750764174.765] [155:157] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-0000000000000020 after 4 ms[0m\n[0;32m[1750764174.765] [155:157] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764174.765] [155:157] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764174.765] [155:157] [DIS] UDP:[fd89:8980:1890:1:439d:a208:448f:f16a%veth5874be4]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-0000000000000020)[0m\n[0;32m[1750764174.765] [155:157] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-0000000000000020 after 4 ms[0m\n[0;32m[1750764174.765] [155:157] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764174.765] [155:157] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764174.765] [155:157] [DIS] UDP:[fd89:8980:1890:1:439d:a208:448f:f16a%veth6dd1a54]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-0000000000000020)[0m\n[0;32m[1750764174.765] [155:157] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-0000000000000020 after 4 ms[0m\n[0;32m[1750764174.765] [155:157] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764174.765] [155:157] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764174.765] [155:157] [DIS] UDP:[fd89:8980:1890:1:439d:a208:448f:f16a%vethb3694c8]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-0000000000000020)[0m\n[0;32m[1750764174.765] [155:157] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-0000000000000020 after 4 ms[0m\n[0;32m[1750764174.765] [155:157] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764174.766] [155:157] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764174.766] [155:157] [DIS] UDP:[fd89:8980:1890:1:439d:a208:448f:f16a%veth0eb2448]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-0000000000000020)[0m\n[0;32m[1750764174.766] [155:157] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-0000000000000020 after 4 ms[0m\n[0;32m[1750764174.766] [155:157] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764174.766] [155:157] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764174.766] [155:157] [DIS] UDP:[fd89:8980:1890:1:439d:a208:448f:f16a%veth9ca026e]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-0000000000000020)[0m\n[0;32m[1750764174.766] [155:157] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-0000000000000020 after 5 ms[0m\n[0;32m[1750764174.766] [155:157] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764174.766] [155:157] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764174.766] [155:157] [DIS] UDP:[fd89:8980:1890:1:439d:a208:448f:f16a%vethd1e126e]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-0000000000000020)[0m\n[0;32m[1750764174.766] [155:157] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-0000000000000020 after 5 ms[0m\n[0;32m[1750764174.766] [155:157] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764174.766] [155:157] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764174.766] [155:157] [DIS] UDP:[fd89:8980:1890:1:439d:a208:448f:f16a%veth1169eeb]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-0000000000000020)[0m\n[0;32m[1750764174.766] [155:157] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-0000000000000020 after 5 ms[0m\n[0;32m[1750764174.766] [155:157] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764174.766] [155:157] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764174.766] [155:157] [DIS] UDP:[fd89:8980:1890:1:439d:a208:448f:f16a%vethfff5d5c]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-0000000000000020)[0m\n[0;32m[1750764174.766] [155:157] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-0000000000000020 after 5 ms[0m\n[0;32m[1750764174.766] [155:157] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764174.766] [155:157] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764174.766] [155:157] [DIS] UDP:[fd89:8980:1890:1:439d:a208:448f:f16a%vethcb6b997]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-0000000000000020)[0m\n[0;32m[1750764174.766] [155:157] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-0000000000000020 after 5 ms[0m\n[0;32m[1750764174.766] [155:157] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764174.767] [155:157] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764174.767] [155:157] [DIS] UDP:[fd89:8980:1890:1:439d:a208:448f:f16a%veth00904c3]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-0000000000000020)[0m\n[0;32m[1750764174.767] [155:157] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-0000000000000020 after 5 ms[0m\n[0;32m[1750764174.767] [155:157] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764174.767] [155:157] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764174.767] [155:157] [DIS] UDP:[fd89:8980:1890:1:439d:a208:448f:f16a%veth334f255]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-0000000000000020)[0m\n[0;32m[1750764174.767] [155:157] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-0000000000000020 after 6 ms[0m\n[0;32m[1750764174.767] [155:157] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764174.767] [155:157] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764174.767] [155:157] [DIS] UDP:[fd89:8980:1890:1:439d:a208:448f:f16a%vetha849f9a]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-0000000000000020)[0m\n[0;32m[1750764174.767] [155:157] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-0000000000000020 after 6 ms[0m\n[0;32m[1750764174.767] [155:157] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764174.767] [155:157] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764174.767] [155:157] [DIS] UDP:[fd89:8980:1890:1:439d:a208:448f:f16a%veth2486638]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-0000000000000020)[0m\n[0;32m[1750764174.767] [155:157] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-0000000000000020 after 6 ms[0m\n[0;32m[1750764174.767] [155:157] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764174.767] [155:157] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764174.767] [155:157] [DIS] UDP:[fd89:8980:1890:1:439d:a208:448f:f16a%vethd6ebaed]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-0000000000000020)[0m\n[0;32m[1750764174.767] [155:157] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-0000000000000020 after 6 ms[0m\n[0;32m[1750764174.767] [155:157] [DIS] Keeping DNSSD lookup active[0m\n[0;32m[1750764174.961] [155:157] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-0000000000000020 after 200 ms[0m\n[0;34m[1750764174.961] [155:157] [DIS] OperationalSessionSetup[1:0000000000000020]: Updating device address to UDP:[fd89:8980:1890:1:439d:a208:448f:f16a]:5540 while in state 2[0m\n[0;34m[1750764174.961] [155:157] [DIS] OperationalSessionSetup[1:0000000000000020]: State change 2 --> 3[0m\n[0;34m[1750764174.961] [155:157] [IN] SecureSession[0x7f7800be60]: Allocated Type:2 LSID:58128[0m\n[0;32m[1750764174.961] [155:157] [SC] Initiating session on local FabricIndex 1 from 0x000000000001B669 -> 0x0000000000000020[0m\n[0;32m[1750764174.961] [155:157] [EM] <<< [E:13032i S:0 M:249271025] (U) Msg TX from 293108CFF6905F8A to 0:0000000000000000 [0000] [UDP:[fd89:8980:1890:1:439d:a208:448f:f16a]:5540] --- Type 0000:30 (SecureChannel:CASE_Sigma1) (B:196)[0m\n[0;32m[1750764174.962] [155:157] [EM] ??1 [E:13032i S:0 M:249271025] (U) Msg Retransmission to 0:0000000000000000 scheduled for 1090ms from now [State:Idle II:800 AI:800 AT:4000][0m\n[0;32m[1750764174.962] [155:157] [SC] Sent Sigma1 msg to <0000000000000020, 1> [II:500ms AI:300ms AT:4000ms][0m\n[0;34m[1750764174.962] [155:157] [DIS] OperationalSessionSetup[1:0000000000000020]: State change 3 --> 4[0m\n[0;32m[1750764175.171] [155:157] [EM] >>> [E:13032i S:0 M:215054347 (Ack:249271025)] (U) Msg RX from 0:0000000000000000 [0000] to 293108CFF6905F8A --- Type 0000:10 (SecureChannel:StandaloneAck) (B:26)[0m\n[0;34m[1750764175.171] [155:157] [EM] Found matching exchange: 13032i, Delegate: 0x7f7800bb78[0m\n[0;34m[1750764175.171] [155:157] [EM] Rxd Ack; Removing MessageCounter:249271025 from Retrans Table on exchange 13032i[0m\n[0;32m[1750764175.379] [155:157] [EM] >>> [E:13032i S:0 M:215054348 (Ack:249271025)] (U) Msg RX from 0:0000000000000000 [0000] to 293108CFF6905F8A --- Type 0000:31 (SecureChannel:CASE_Sigma2) (B:751)[0m\n[0;34m[1750764175.379] [155:157] [EM] Found matching exchange: 13032i, Delegate: 0x7f7800bb78[0m\n[0;34m[1750764175.379] [155:157] [EM] CHIP MessageCounter:249271025 not in RetransTable on exchange 13032i[0m\n[0;32m[1750764175.379] [155:157] [SC] Received Sigma2 msg[0m\n[0;34m[1750764175.379] [155:157] [SC] Found MRP parameters in the message[0m\n[0;34m[1750764175.382] [155:157] [SC] Peer <0000000000000020, 1> assigned session ID 19986[0m\n[0;34m[1750764175.382] [155:157] [SC] Sending Sigma3[0m\n[0;32m[1750764175.382] [155:157] [EM] <<< [E:13032i S:0 M:249271026 (Ack:215054348)] (U) Msg TX from 293108CFF6905F8A to 0:0000000000000000 [0000] [UDP:[fd89:8980:1890:1:439d:a208:448f:f16a]:5540] --- Type 0000:32 (SecureChannel:CASE_Sigma3) (B:598)[0m\n[0;32m[1750764175.382] [155:157] [EM] ??1 [E:13032i S:0 M:249271026] (U) Msg Retransmission to 0:0000000000000000 scheduled for 1033ms from now [State:Active II:800 AI:800 AT:4000][0m\n[0;32m[1750764175.382] [155:157] [SC] Sent Sigma3 msg[0m\n[0;32m[1750764175.700] [155:157] [EM] >>> [E:13032i S:0 M:215054349 (Ack:249271026)] (U) Msg RX from 0:0000000000000000 [0000] to 293108CFF6905F8A --- Type 0000:10 (SecureChannel:StandaloneAck) (B:26)[0m\n[0;34m[1750764175.700] [155:157] [EM] Found matching exchange: 13032i, Delegate: 0x7f7800bb78[0m\n[0;34m[1750764175.700] [155:157] [EM] Rxd Ack; Removing MessageCounter:249271026 from Retrans Table on exchange 13032i[0m\n[0;32m[1750764175.872] [155:157] [EM] >>> [E:13032i S:0 M:215054350 (Ack:249271026)] (U) Msg RX from 0:0000000000000000 [0000] to 293108CFF6905F8A --- Type 0000:40 (SecureChannel:StatusReport) (B:34)[0m\n[0;34m[1750764175.872] [155:157] [EM] Found matching exchange: 13032i, Delegate: 0x7f7800bb78[0m\n[0;34m[1750764175.872] [155:157] [EM] CHIP MessageCounter:249271026 not in RetransTable on exchange 13032i[0m\n[0;32m[1750764175.872] [155:157] [SC] Success status report received. Session was established[0m\n[0;32m[1750764175.881] [155:157] [SC] SecureSession[0x7f7800be60, LSID:58128]: State change 'kEstablishing' --> 'kActive'[0m\n[0;34m[1750764175.881] [155:157] [IN] SecureSession[0x7f7800be60]: Activated - Type:2 LSID:58128[0m\n[0;34m[1750764175.881] [155:157] [IN] New secure session activated for device <0000000000000020, 1>, LSID:58128 PSID:19986![0m\n[0;34m[1750764175.881] [155:157] [DIS] OperationalSessionSetup[1:0000000000000020]: State change 4 --> 5[0m\n[0;32m[1750764175.881] [155:157] [TOO] \tcluster 0x0000_001F, attribute: 0x0000_0000, endpoint 0[0m\n[0;34m[1750764175.881] [155:157] [DMG] WriteClient moving to [AddAttribu][0m\n[0;32m[1750764175.881] [155:157] [EM] <<< [E:13033i S:58128 M:102874214] (S) Msg TX from 000000000001B669 to 1:0000000000000020 [6BB7] [UDP:[fd89:8980:1890:1:439d:a208:448f:f16a]:5540] --- Type 0001:06 (IM:WriteRequest) (B:88)[0m\n[0;32m[1750764175.881] [155:157] [EM] ??1 [E:13033i S:58128 M:102874214] (S) Msg Retransmission to 1:0000000000000020 scheduled for 1013ms from now [State:Active II:800 AI:800 AT:4000][0m\n[0;34m[1750764175.881] [155:157] [DMG] WriteClient moving to [AwaitingRe][0m\n[0;32m[1750764175.881] [155:157] [EM] <<< [E:13032i S:0 M:249271027 (Ack:215054350)] (U) Msg TX from 293108CFF6905F8A to 0:0000000000000000 [0000] [UDP:[fd89:8980:1890:1:439d:a208:448f:f16a]:5540] --- Type 0000:10 (SecureChannel:StandaloneAck) (B:26)[0m\n[0;34m[1750764175.881] [155:157] [EM] Flushed pending ack for MessageCounter:215054350 on exchange 13032i[0m\n[0;34m[1750764175.881] [155:157] [DL] HandlePlatformSpecificBLEEvent 32792[0m\n[0;32m[1750764176.024] [155:157] [EM] >>> [E:13033i S:58128 M:224750508 (Ack:102874214)] (S) Msg RX from 1:0000000000000020 [6BB7] to 000000000001B669 --- Type 0001:07 (IM:WriteResponse) (B:62)[0m\n[0;34m[1750764176.024] [155:157] [EM] Found matching exchange: 13033i, Delegate: 0x7f7800a280[0m\n[0;34m[1750764176.024] [155:157] [EM] Rxd Ack; Removing MessageCounter:102874214 from Retrans Table on exchange 13033i[0m\n[0;34m[1750764176.024] [155:157] [DMG] WriteClient moving to [ResponseRe][0m\n[0;34m[1750764176.024] [155:157] [DMG] WriteClient moving to [AwaitingDe][0m\n[0;32m[1750764176.024] [155:157] [EM] <<< [E:13033i S:58128 M:102874215 (Ack:224750508)] (S) Msg TX from 000000000001B669 to 1:0000000000000020 [6BB7] [UDP:[fd89:8980:1890:1:439d:a208:448f:f16a]:5540] --- Type 0000:10 (SecureChannel:StandaloneAck) (B:34)[0m\n[0;34m[1750764176.024] [155:157] [EM] Flushed pending ack for MessageCounter:224750508 on exchange 13033i[0m\n[0;34m[1750764176.024] [155:155] [CTL] Shutting down the commissioner[0m\n[0;32m[1750764176.024] [155:155] [PAF] WiFiPAF: Closing all WiFiPAF sessions to shutdown[0m\n[0;34m[1750764176.024] [155:155] [CTL] Shutting down the controller[0m\n[0;34m[1750764176.024] [155:155] [IN] Expiring all sessions for fabric 0x1!![0m\n[0;34m[1750764176.024] [155:155] [IN] SecureSession[0x7f7800be60]: MarkForEviction Type:2 LSID:58128[0m\n[0;32m[1750764176.024] [155:155] [SC] SecureSession[0x7f7800be60, LSID:58128]: State change 'kActive' --> 'kPendingEviction'[0m\n[0;34m[1750764176.024] [155:155] [IN] SecureSession[0x7f7800be60]: Released - Type:2 LSID:58128[0m\n[0;32m[1750764176.024] [155:155] [FP] Forgetting fabric 0x1[0m\n[0;32m[1750764176.024] [155:155] [TS] Pending Last Known Good Time: 2023-10-14T01:16:48[0m\n[0;32m[1750764176.024] [155:155] [TS] Previous Last Known Good Time: 2023-10-14T01:16:48[0m\n[0;32m[1750764176.024] [155:155] [TS] Reverted Last Known Good Time to previous value[0m\n[0;34m[1750764176.024] [155:155] [CTL] Shutting down the commissioner[0m\n[0;32m[1750764176.024] [155:155] [PAF] WiFiPAF: Closing all WiFiPAF sessions to shutdown[0m\n[0;34m[1750764176.024] [155:155] [CTL] Shutting down the controller[0m\n[0;34m[1750764176.024] [155:155] [CTL] Shutting down the System State, this will teardown the CHIP Stack[0m\n[0;34m[1750764176.025] [155:155] [DMG] All ReadHandler-s are clean, clear GlobalDirtySet[0m\n[0;32m[1750764176.025] [155:155] [FP] Shutting down FabricTable[0m\n[0;32m[1750764176.025] [155:155] [TS] Pending Last Known Good Time: 2023-10-14T01:16:48[0m\n[0;32m[1750764176.025] [155:155] [TS] Previous Last Known Good Time: 2023-10-14T01:16:48[0m\n[0;32m[1750764176.025] [155:155] [TS] Reverted Last Known Good Time to previous value[0m\n[0;34m[1750764176.034] [155:155] [DL] Wrote settings to /tmp/chip_counters.ini[0m\n[0;32m[1750764176.034] [155:155] [DL] NVS set: chip-counters/total-operational-hours = 0 (0x0)[0m\n[0;32m[1750764176.034] [155:155] [DL] Inet Layer shutdown[0m\n[0;32m[1750764176.034] [155:155] [DL] BLE Layer shutdown[0m\n[0;32m[1750764176.034] [155:155] [DL] WiFi-PAF Layer shutdown[0m\n[0;32m[1750764176.034] [155:155] [PAF] WiFiPAF: Closing all WiFiPAF sessions to shutdown[0m\n[0;32m[1750764176.034] [155:155] [DL] System Layer shutdown[0m\n"
    },
    "binding_command": {
        "command": "/app/connected_home_ip/out/chip-tool-linux/chip-tool binding write binding [{\"node\":32, \"endpoint\":1, \"cluster\":6}] 31 1",
        "returncode": 0,
        "stderr": "",
        "stdout":"[0;34m[1750764176.076] [158:158] [DL] ChipLinuxStorage::Init: Using KVS config file: /tmp/chip_tool_kvs[0m\n[1;31m[1750764176.077] [158:158] [DL] ChipLinuxStorage::Init: Attempt to re-initialize with KVS config file: /tmp/chip_kvs[0m\n[0;34m[1750764176.079] [158:158] [DL] ChipLinuxStorage::Init: Using KVS config file: /tmp/chip_factory.ini[0m\n[0;34m[1750764176.079] [158:158] [DL] ChipLinuxStorage::Init: Using KVS config file: /tmp/chip_config.ini[0m\n[0;34m[1750764176.079] [158:158] [DL] ChipLinuxStorage::Init: Using KVS config file: /tmp/chip_counters.ini[0m\n[0;34m[1750764176.080] [158:158] [DL] Wrote settings to /tmp/chip_counters.ini[0m\n[0;32m[1750764176.080] [158:158] [DL] NVS set: chip-counters/reboot-count = 38 (0x26)[0m\n[0;32m[1750764176.081] [158:158] [DL] Got Ethernet interface: end0[0m\n[0;32m[1750764176.081] [158:158] [DL] Found the primary Ethernet interface:end0[0m\n[0;32m[1750764176.081] [158:158] [DL] Got WiFi interface: wlan0[0m\n[1;31m[1750764176.081] [158:158] [DL] Failed to reset WiFi statistic counts[0m\n[0;32m[1750764176.081] [158:158] [PAF] WiFiPAF: WiFiPAFLayer::Init()[0m\n[0;34m[1750764176.081] [158:158] [IN] UDP::Init bind&listen port=0[0m\n[0;34m[1750764176.081] [158:158] [IN] UDP::Init bound to port=48518[0m\n[0;34m[1750764176.081] [158:158] [IN] UDP::Init bind&listen port=0[0m\n[0;34m[1750764176.081] [158:158] [IN] UDP::Init bound to port=48988[0m\n[0;34m[1750764176.081] [158:158] [IN] BLEBase::Init - setting/overriding transport[0m\n[0;34m[1750764176.081] [158:158] [IN] WiFiPAFBase::Init - setting/overriding transport[0m\n[0;34m[1750764176.081] [158:158] [IN] TransportMgr initialized[0m\n[0;34m[1750764176.081] [158:158] [FP] Initializing FabricTable from persistent storage[0m\n[0;32m[1750764176.081] [158:158] [TS] Last Known Good Time: 2023-10-14T01:16:48[0m\n[0;32m[1750764176.081] [158:158] [FP] Fabric index 0x1 was retrieved from storage. Compressed FabricId 0x7C3E96F4C2D76BB7, FabricId 0x0000000000000001, NodeId 0x000000000001B669, VendorId 0xFFF1[0m\n[0;32m[1750764176.082] [158:158] [DMG] Ember attribute persistence requires setting up[0m\n[0;32m[1750764176.082] [158:158] [ZCL] Using ZAP configuration...[0m\n[0;34m[1750764176.090] [158:158] [CTL] System State Initialized...[0m\n[0;32m[1750764176.090] [158:158] [CTL] Setting attestation nonce to random value[0m\n[0;32m[1750764176.090] [158:158] [CTL] Setting CSR nonce to random value[0m\n[0;34m[1750764176.090] [158:160] [DL] CHIP task running[0m\n[0;34m[1750764176.090] [158:160] [DL] HandlePlatformSpecificBLEEvent 32786[0m\n[0;32m[1750764176.090] [158:160] [CTL] Setting attestation nonce to random value[0m\n[0;32m[1750764176.090] [158:160] [CTL] Setting CSR nonce to random value[0m\n[0;32m[1750764176.090] [158:160] [CTL] Generating NOC[0m\n[0;32m[1750764176.091] [158:160] [FP] Validating NOC chain[0m\n[0;32m[1750764176.092] [158:160] [FP] NOC chain validation successful[0m\n[0;32m[1750764176.092] [158:160] [FP] Updated fabric at index: 0x1, Node ID: 0x000000000001B669[0m\n[0;32m[1750764176.092] [158:160] [TS] Last Known Good Time: 2023-10-14T01:16:48[0m\n[0;32m[1750764176.092] [158:160] [TS] New proposed Last Known Good Time: 2021-01-01T00:00:00[0m\n[0;32m[1750764176.092] [158:160] [TS] Retaining current Last Known Good Time[0m\n[0;32m[1750764176.092] [158:160] [FP] Metadata for Fabric 0x1 persisted to storage.[0m\n[0;32m[1750764176.093] [158:160] [TS] Committing Last Known Good Time to storage: 2023-10-14T01:16:48[0m\n[0;32m[1750764176.093] [158:160] [CTL] Joined the fabric at index 1. Fabric ID is 0x0000000000000001 (Compressed Fabric ID: 7C3E96F4C2D76BB7)[0m\n[0;32m[1750764176.095] [158:160] [TOO] Sending command to node 0x1f[0m\n[0;34m[1750764176.095] [158:160] [CSM] FindOrEstablishSession: PeerId = [1:000000000000001F][0m\n[0;34m[1750764176.095] [158:160] [CSM] FindOrEstablishSession: No existing OperationalSessionSetup instance found[0m\n[0;34m[1750764176.095] [158:160] [DIS] OperationalSessionSetup[1:000000000000001F]: State change 1 --> 2[0m\n[0;32m[1750764176.097] [158:160] [DIS] Lookup started for 7C3E96F4C2D76BB7-000000000000001F[0m\n[0;34m[1750764176.097] [158:160] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764176.097] [158:160] [DIS] UDP:[fd89:8980:1890:1:21dc:8fbb:4744:37da%end0]:5540: new best score: 5 (for 7C3E96F4C2D76BB7-000000000000001F)[0m\n[0;32m[1750764176.097] [158:160] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-000000000000001F after 2 ms[0m\n[0;32m[1750764176.097] [158:160] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764176.097] [158:160] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764176.097] [158:160] [DIS] UDP:[fd89:8980:1890:1:21dc:8fbb:4744:37da%end0]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-000000000000001F)[0m\n[0;32m[1750764176.097] [158:160] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-000000000000001F after 2 ms[0m\n[0;32m[1750764176.097] [158:160] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764176.098] [158:160] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764176.098] [158:160] [DIS] UDP:[fd89:8980:1890:1:21dc:8fbb:4744:37da%hassio]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-000000000000001F)[0m\n[0;32m[1750764176.098] [158:160] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-000000000000001F after 3 ms[0m\n[0;32m[1750764176.098] [158:160] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764176.098] [158:160] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764176.098] [158:160] [DIS] UDP:[fd89:8980:1890:1:21dc:8fbb:4744:37da%hassio]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-000000000000001F)[0m\n[0;32m[1750764176.098] [158:160] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-000000000000001F after 3 ms[0m\n[0;32m[1750764176.098] [158:160] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764176.098] [158:160] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764176.098] [158:160] [DIS] UDP:[fd89:8980:1890:1:21dc:8fbb:4744:37da%docker0]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-000000000000001F)[0m\n[0;32m[1750764176.098] [158:160] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-000000000000001F after 3 ms[0m\n[0;32m[1750764176.098] [158:160] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764176.098] [158:160] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764176.098] [158:160] [DIS] UDP:[fd89:8980:1890:1:21dc:8fbb:4744:37da%docker0]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-000000000000001F)[0m\n[0;32m[1750764176.098] [158:160] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-000000000000001F after 3 ms[0m\n[0;32m[1750764176.098] [158:160] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764176.099] [158:160] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764176.099] [158:160] [DIS] UDP:[fd89:8980:1890:1:21dc:8fbb:4744:37da%veth5874be4]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-000000000000001F)[0m\n[0;32m[1750764176.099] [158:160] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-000000000000001F after 3 ms[0m\n[0;32m[1750764176.099] [158:160] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764176.099] [158:160] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764176.099] [158:160] [DIS] UDP:[fd89:8980:1890:1:21dc:8fbb:4744:37da%veth6dd1a54]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-000000000000001F)[0m\n[0;32m[1750764176.099] [158:160] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-000000000000001F after 4 ms[0m\n[0;32m[1750764176.099] [158:160] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764176.099] [158:160] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764176.099] [158:160] [DIS] UDP:[fd89:8980:1890:1:21dc:8fbb:4744:37da%vethb3694c8]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-000000000000001F)[0m\n[0;32m[1750764176.099] [158:160] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-000000000000001F after 4 ms[0m\n[0;32m[1750764176.099] [158:160] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764176.099] [158:160] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764176.099] [158:160] [DIS] UDP:[fd89:8980:1890:1:21dc:8fbb:4744:37da%veth0eb2448]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-000000000000001F)[0m\n[0;32m[1750764176.099] [158:160] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-000000000000001F after 4 ms[0m\n[0;32m[1750764176.099] [158:160] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764176.099] [158:160] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764176.099] [158:160] [DIS] UDP:[fd89:8980:1890:1:21dc:8fbb:4744:37da%veth9ca026e]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-000000000000001F)[0m\n[0;32m[1750764176.099] [158:160] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-000000000000001F after 4 ms[0m\n[0;32m[1750764176.099] [158:160] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764176.100] [158:160] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764176.100] [158:160] [DIS] UDP:[fd89:8980:1890:1:21dc:8fbb:4744:37da%vethd1e126e]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-000000000000001F)[0m\n[0;32m[1750764176.100] [158:160] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-000000000000001F after 4 ms[0m\n[0;32m[1750764176.100] [158:160] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764176.100] [158:160] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764176.100] [158:160] [DIS] UDP:[fd89:8980:1890:1:21dc:8fbb:4744:37da%veth1169eeb]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-000000000000001F)[0m\n[0;32m[1750764176.100] [158:160] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-000000000000001F after 5 ms[0m\n[0;32m[1750764176.100] [158:160] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764176.100] [158:160] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764176.100] [158:160] [DIS] UDP:[fd89:8980:1890:1:21dc:8fbb:4744:37da%vethfff5d5c]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-000000000000001F)[0m\n[0;32m[1750764176.100] [158:160] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-000000000000001F after 5 ms[0m\n[0;32m[1750764176.100] [158:160] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764176.100] [158:160] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764176.100] [158:160] [DIS] UDP:[fd89:8980:1890:1:21dc:8fbb:4744:37da%vethcb6b997]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-000000000000001F)[0m\n[0;32m[1750764176.100] [158:160] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-000000000000001F after 5 ms[0m\n[0;32m[1750764176.100] [158:160] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764176.100] [158:160] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764176.100] [158:160] [DIS] UDP:[fd89:8980:1890:1:21dc:8fbb:4744:37da%veth00904c3]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-000000000000001F)[0m\n[0;32m[1750764176.100] [158:160] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-000000000000001F after 5 ms[0m\n[0;32m[1750764176.100] [158:160] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764176.100] [158:160] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764176.100] [158:160] [DIS] UDP:[fd89:8980:1890:1:21dc:8fbb:4744:37da%veth334f255]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-000000000000001F)[0m\n[0;32m[1750764176.100] [158:160] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-000000000000001F after 5 ms[0m\n[0;32m[1750764176.100] [158:160] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764176.101] [158:160] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764176.101] [158:160] [DIS] UDP:[fd89:8980:1890:1:21dc:8fbb:4744:37da%vetha849f9a]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-000000000000001F)[0m\n[0;32m[1750764176.101] [158:160] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-000000000000001F after 6 ms[0m\n[0;32m[1750764176.101] [158:160] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764176.101] [158:160] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764176.101] [158:160] [DIS] UDP:[fd89:8980:1890:1:21dc:8fbb:4744:37da%veth2486638]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-000000000000001F)[0m\n[0;32m[1750764176.101] [158:160] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-000000000000001F after 6 ms[0m\n[0;32m[1750764176.101] [158:160] [DIS] Keeping DNSSD lookup active[0m\n[0;34m[1750764176.101] [158:160] [DIS] Lookup clearing interface for non LL address[0m\n[0;32m[1750764176.101] [158:160] [DIS] UDP:[fd89:8980:1890:1:21dc:8fbb:4744:37da%vethd6ebaed]:5540: score has not improved: 5 (for 7C3E96F4C2D76BB7-000000000000001F)[0m\n[0;32m[1750764176.101] [158:160] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-000000000000001F after 6 ms[0m\n[0;32m[1750764176.101] [158:160] [DIS] Keeping DNSSD lookup active[0m\n[0;32m[1750764176.295] [158:160] [DIS] Checking node lookup status for 7C3E96F4C2D76BB7-000000000000001F after 200 ms[0m\n[0;34m[1750764176.295] [158:160] [DIS] OperationalSessionSetup[1:000000000000001F]: Updating device address to UDP:[fd89:8980:1890:1:21dc:8fbb:4744:37da]:5540 while in state 2[0m\n[0;34m[1750764176.295] [158:160] [DIS] OperationalSessionSetup[1:000000000000001F]: State change 2 --> 3[0m\n[0;34m[1750764176.295] [158:160] [IN] SecureSession[0x7f8800be60]: Allocated Type:2 LSID:60051[0m\n[0;32m[1750764176.295] [158:160] [SC] Initiating session on local FabricIndex 1 from 0x000000000001B669 -> 0x000000000000001F[0m\n[0;32m[1750764176.296] [158:160] [EM] <<< [E:35101i S:0 M:46570706] (U) Msg TX from D77187D19F8536B5 to 0:0000000000000000 [0000] [UDP:[fd89:8980:1890:1:21dc:8fbb:4744:37da]:5540] --- Type 0000:30 (SecureChannel:CASE_Sigma1) (B:196)[0m\n[0;32m[1750764176.296] [158:160] [EM] ??1 [E:35101i S:0 M:46570706] (U) Msg Retransmission to 0:0000000000000000 scheduled for 960ms from now [State:Idle II:800 AI:800 AT:4000][0m\n[0;32m[1750764176.296] [158:160] [SC] Sent Sigma1 msg to <000000000000001F, 1> [II:500ms AI:300ms AT:4000ms][0m\n[0;34m[1750764176.296] [158:160] [DIS] OperationalSessionSetup[1:000000000000001F]: State change 3 --> 4[0m\n[0;32m[1750764176.414] [158:160] [EM] >>> [E:35101i S:0 M:32641706 (Ack:46570706)] (U) Msg RX from 0:0000000000000000 [0000] to D77187D19F8536B5 --- Type 0000:10 (SecureChannel:StandaloneAck) (B:26)[0m\n[0;34m[1750764176.414] [158:160] [EM] Found matching exchange: 35101i, Delegate: 0x7f8800bb78[0m\n[0;34m[1750764176.414] [158:160] [EM] Rxd Ack; Removing MessageCounter:46570706 from Retrans Table on exchange 35101i[0m\n[0;32m[1750764176.642] [158:160] [EM] >>> [E:35101i S:0 M:32641707 (Ack:46570706)] (U) Msg RX from 0:0000000000000000 [0000] to D77187D19F8536B5 --- Type 0000:31 (SecureChannel:CASE_Sigma2) (B:751)[0m\n[0;34m[1750764176.642] [158:160] [EM] Found matching exchange: 35101i, Delegate: 0x7f8800bb78[0m\n[0;34m[1750764176.642] [158:160] [EM] CHIP MessageCounter:46570706 not in RetransTable on exchange 35101i[0m\n[0;32m[1750764176.642] [158:160] [SC] Received Sigma2 msg[0m\n[0;34m[1750764176.642] [158:160] [SC] Found MRP parameters in the message[0m\n[0;34m[1750764176.645] [158:160] [SC] Peer <000000000000001F, 1> assigned session ID 57724[0m\n[0;34m[1750764176.645] [158:160] [SC] Sending Sigma3[0m\n[0;32m[1750764176.645] [158:160] [EM] <<< [E:35101i S:0 M:46570707 (Ack:32641707)] (U) Msg TX from D77187D19F8536B5 to 0:0000000000000000 [0000] [UDP:[fd89:8980:1890:1:21dc:8fbb:4744:37da]:5540] --- Type 0000:32 (SecureChannel:CASE_Sigma3) (B:598)[0m\n[0;32m[1750764176.645] [158:160] [EM] ??1 [E:35101i S:0 M:46570707] (U) Msg Retransmission to 0:0000000000000000 scheduled for 1030ms from now [State:Active II:800 AI:800 AT:4000][0m\n[0;32m[1750764176.645] [158:160] [SC] Sent Sigma3 msg[0m\n[0;32m[1750764176.935] [158:160] [EM] >>> [E:35101i S:0 M:32641708 (Ack:46570707)] (U) Msg RX from 0:0000000000000000 [0000] to D77187D19F8536B5 --- Type 0000:10 (SecureChannel:StandaloneAck) (B:26)[0m\n[0;34m[1750764176.935] [158:160] [EM] Found matching exchange: 35101i, Delegate: 0x7f8800bb78[0m\n[0;34m[1750764176.935] [158:160] [EM] Rxd Ack; Removing MessageCounter:46570707 from Retrans Table on exchange 35101i[0m\n[0;32m[1750764177.085] [158:160] [EM] >>> [E:35101i S:0 M:32641709 (Ack:46570707)] (U) Msg RX from 0:0000000000000000 [0000] to D77187D19F8536B5 --- Type 0000:40 (SecureChannel:StatusReport) (B:34)[0m\n[0;34m[1750764177.085] [158:160] [EM] Found matching exchange: 35101i, Delegate: 0x7f8800bb78[0m\n[0;34m[1750764177.085] [158:160] [EM] CHIP MessageCounter:46570707 not in RetransTable on exchange 35101i[0m\n[0;32m[1750764177.085] [158:160] [SC] Success status report received. Session was established[0m\n[0;32m[1750764177.093] [158:160] [SC] SecureSession[0x7f8800be60, LSID:60051]: State change 'kEstablishing' --> 'kActive'[0m\n[0;34m[1750764177.093] [158:160] [IN] SecureSession[0x7f8800be60]: Activated - Type:2 LSID:60051[0m\n[0;34m[1750764177.093] [158:160] [IN] New secure session activated for device <000000000000001F, 1>, LSID:60051 PSID:57724![0m\n[0;34m[1750764177.093] [158:160] [DIS] OperationalSessionSetup[1:000000000000001F]: State change 4 --> 5[0m\n[0;32m[1750764177.093] [158:160] [TOO] \tcluster 0x0000_001E, attribute: 0x0000_0000, endpoint 1[0m\n[0;34m[1750764177.093] [158:160] [DMG] WriteClient moving to [AddAttribu][0m\n[0;34m[1750764177.093] [158:160] [DMG] WriteClient moving to [AddAttribu][0m\n[0;32m[1750764177.093] [158:160] [EM] <<< [E:35102i S:60051 M:122021588] (S) Msg TX from 000000000001B669 to 1:000000000000001F [6BB7] [UDP:[fd89:8980:1890:1:21dc:8fbb:4744:37da]:5540] --- Type 0001:06 (IM:WriteRequest) (B:89)[0m\n[0;32m[1750764177.093] [158:160] [EM] ??1 [E:35102i S:60051 M:122021588] (S) Msg Retransmission to 1:000000000000001F scheduled for 966ms from now [State:Active II:800 AI:800 AT:4000][0m\n[0;34m[1750764177.093] [158:160] [DMG] WriteClient moving to [AwaitingRe][0m\n[0;32m[1750764177.093] [158:160] [EM] <<< [E:35101i S:0 M:46570708 (Ack:32641709)] (U) Msg TX from D77187D19F8536B5 to 0:0000000000000000 [0000] [UDP:[fd89:8980:1890:1:21dc:8fbb:4744:37da]:5540] --- Type 0000:10 (SecureChannel:StandaloneAck) (B:26)[0m\n[0;34m[1750764177.093] [158:160] [EM] Flushed pending ack for MessageCounter:32641709 on exchange 35101i[0m\n[0;34m[1750764177.093] [158:160] [DL] HandlePlatformSpecificBLEEvent 32792[0m\n[0;32m[1750764177.303] [158:160] [EM] >>> [E:35102i S:60051 M:40088292 (Ack:122021588)] (S) Msg RX from 1:000000000000001F [6BB7] to 000000000001B669 --- Type 0001:07 (IM:WriteResponse) (B:84)[0m\n[0;34m[1750764177.303] [158:160] [EM] Found matching exchange: 35102i, Delegate: 0x7f8800a280[0m\n[0;34m[1750764177.303] [158:160] [EM] Rxd Ack; Removing MessageCounter:122021588 from Retrans Table on exchange 35102i[0m\n[0;34m[1750764177.303] [158:160] [DMG] WriteClient moving to [ResponseRe][0m\n[0;34m[1750764177.303] [158:160] [DMG] WriteClient moving to [AwaitingDe][0m\n[0;32m[1750764177.303] [158:160] [EM] <<< [E:35102i S:60051 M:122021589 (Ack:40088292)] (S) Msg TX from 000000000001B669 to 1:000000000000001F [6BB7] [UDP:[fd89:8980:1890:1:21dc:8fbb:4744:37da]:5540] --- Type 0000:10 (SecureChannel:StandaloneAck) (B:34)[0m\n[0;34m[1750764177.303] [158:160] [EM] Flushed pending ack for MessageCounter:40088292 on exchange 35102i[0m\n[0;34m[1750764177.303] [158:158] [CTL] Shutting down the commissioner[0m\n[0;32m[1750764177.303] [158:158] [PAF] WiFiPAF: Closing all WiFiPAF sessions to shutdown[0m\n[0;34m[1750764177.303] [158:158] [CTL] Shutting down the controller[0m\n[0;34m[1750764177.303] [158:158] [IN] Expiring all sessions for fabric 0x1!![0m\n[0;34m[1750764177.303] [158:158] [IN] SecureSession[0x7f8800be60]: MarkForEviction Type:2 LSID:60051[0m\n[0;32m[1750764177.303] [158:158] [SC] SecureSession[0x7f8800be60, LSID:60051]: State change 'kActive' --> 'kPendingEviction'[0m\n[0;34m[1750764177.303] [158:158] [IN] SecureSession[0x7f8800be60]: Released - Type:2 LSID:60051[0m\n[0;32m[1750764177.303] [158:158] [FP] Forgetting fabric 0x1[0m\n[0;32m[1750764177.303] [158:158] [TS] Pending Last Known Good Time: 2023-10-14T01:16:48[0m\n[0;32m[1750764177.303] [158:158] [TS] Previous Last Known Good Time: 2023-10-14T01:16:48[0m\n[0;32m[1750764177.303] [158:158] [TS] Reverted Last Known Good Time to previous value[0m\n[0;34m[1750764177.303] [158:158] [CTL] Shutting down the commissioner[0m\n[0;32m[1750764177.303] [158:158] [PAF] WiFiPAF: Closing all WiFiPAF sessions to shutdown[0m\n[0;34m[1750764177.303] [158:158] [CTL] Shutting down the controller[0m\n[0;34m[1750764177.303] [158:158] [CTL] Shutting down the System State, this will teardown the CHIP Stack[0m\n[0;34m[1750764177.304] [158:158] [DMG] All ReadHandler-s are clean, clear GlobalDirtySet[0m\n[0;32m[1750764177.304] [158:158] [FP] Shutting down FabricTable[0m\n[0;32m[1750764177.304] [158:158] [TS] Pending Last Known Good Time: 2023-10-14T01:16:48[0m\n[0;32m[1750764177.304] [158:158] [TS] Previous Last Known Good Time: 2023-10-14T01:16:48[0m\n[0;32m[1750764177.304] [158:158] [TS] Reverted Last Known Good Time to previous value[0m\n[0;34m[1750764177.313] [158:158] [DL] Wrote settings to /tmp/chip_counters.ini[0m\n[0;32m[1750764177.313] [158:158] [DL] NVS set: chip-counters/total-operational-hours = 0 (0x0)[0m\n[0;32m[1750764177.313] [158:158] [DL] Inet Layer shutdown[0m\n[0;32m[1750764177.313] [158:158] [DL] BLE Layer shutdown[0m\n[0;32m[1750764177.313] [158:158] [DL] WiFi-PAF Layer shutdown[0m\n[0;32m[1750764177.313] [158:158] [PAF] WiFiPAF: Closing all WiFiPAF sessions to shutdown[0m\n[0;32m[1750764177.313] [158:158] [DL] System Layer shutdown[0m\n"
    }
}