// Tests for the schnell-home-automation app
//
// This file contains unit tests for the Matter binding functionality
// and API response handling.

import 'package:flutter_test/flutter_test.dart';
import 'package:http/http.dart' as http;
import 'package:homeautomation/services/matter_binding_service.dart';

void main() {
  group('ChipToolResponse', () {
    test('should parse successful commission response correctly', () {
      final mockResponse = http.Response(
        '{"returncode": 0, "stdout": "Device commissioning completed with success\\nShutting down", "stderr": ""}',
        200,
      );

      final chipToolResponse = ChipToolResponse.fromHttpResponse(
        mockResponse,
        operationType: 'commission',
      );

      expect(chipToolResponse.success, isTrue);
      expect(chipToolResponse.errorMessage, isNull);
      expect(chipToolResponse.returnCode, equals(0));
      expect(chipToolResponse.stdout, contains('Shutting down'));
    });

    test('should parse failed commission response correctly', () {
      final mockResponse = http.Response(
        '{"returncode": 1, "stdout": "Failed to commission device", "stderr": "Connection timeout"}',
        200,
      );

      final chipToolResponse = ChipToolResponse.fromHttpResponse(
        mockResponse,
        operationType: 'commission',
      );

      expect(chipToolResponse.success, isFalse);
      expect(chipToolResponse.errorMessage, isNotNull);
      expect(chipToolResponse.returnCode, equals(1));
      expect(chipToolResponse.errorMessage, contains('commission'));
    });

    test('should handle HTTP error responses', () {
      final mockResponse = http.Response('Not Found', 404);

      final chipToolResponse = ChipToolResponse.fromHttpResponse(mockResponse);

      expect(chipToolResponse.success, isFalse);
      expect(chipToolResponse.errorMessage, contains('HTTP 404'));
    });

    test('should handle malformed JSON responses', () {
      final mockResponse = http.Response('invalid json', 200);

      final chipToolResponse = ChipToolResponse.fromHttpResponse(mockResponse);

      expect(chipToolResponse.success, isFalse);
      expect(
        chipToolResponse.errorMessage,
        contains('Failed to parse response'),
      );
    });

    test('should recognize clear storage success message', () {
      final mockResponse = http.Response(
        '{"returncode": 0, "stdout": "Clearing Default storage\\nShutting down", "stderr": ""}',
        200,
      );

      final chipToolResponse = ChipToolResponse.fromHttpResponse(
        mockResponse,
        operationType: 'clear',
      );

      expect(chipToolResponse.success, isTrue);
      expect(chipToolResponse.stdout, contains('Clearing Default storage'));
    });

    test('should recognize binding success patterns', () {
      final mockResponse = http.Response(
        '{"returncode": 0, "stdout": "Binding table entry added\\nShutting down", "stderr": ""}',
        200,
      );

      final chipToolResponse = ChipToolResponse.fromHttpResponse(
        mockResponse,
        operationType: 'binding',
      );

      expect(chipToolResponse.success, isTrue);
      expect(chipToolResponse.stdout, contains('Binding table entry added'));
    });
  });

  group('MatterBindingService', () {
    test('should initialize with default devices', () {
      final service = MatterBindingService();

      // The service should initialize with some default devices
      expect(service.devices, isNotEmpty);
      expect(service.bindings, isEmpty);
    });
  });
}
