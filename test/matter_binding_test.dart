// This is a basic Flutter widget test.
//
// This file contains unit tests for the Matter binding functionality
// and API response handling.

import 'package:flutter_test/flutter_test.dart';
import 'package:dio/dio.dart';
import 'package:homeautomation/services/matter_binding_service.dart';

void main() {
  group('ChipToolResponse', () {
    test('should parse successful commission response correctly', () {
      final mockResponse = Response(
        data: {
          'returncode': 0,
          'stdout':
              'Device commissioning completed with success\nShutting down',
          'stderr': '',
        },
        statusCode: 200,
        requestOptions: RequestOptions(path: '/pair'),
      );

      final chipToolResponse = ChipToolResponse.fromDioResponse(
        mockResponse,
        operationType: 'commission',
      );

      expect(chipToolResponse.success, isTrue);
      expect(chipToolResponse.errorMessage, isNull);
      expect(chipToolResponse.returnCode, equals(0));
      expect(chipToolResponse.stdout, contains('Shutting down'));
    });

    test('should parse failed commission response correctly', () {
      final mockResponse = Response(
        data: {
          'returncode': 1,
          'stdout': 'Failed to commission device',
          'stderr': 'Connection timeout',
        },
        statusCode: 200,
        requestOptions: RequestOptions(path: '/pair'),
      );

      final chipToolResponse = ChipToolResponse.fromDioResponse(
        mockResponse,
        operationType: 'commission',
      );

      expect(chipToolResponse.success, isFalse);
      expect(chipToolResponse.errorMessage, isNotNull);
      expect(chipToolResponse.returnCode, equals(1));
      expect(chipToolResponse.errorMessage, contains('commission'));
    });

    test('should handle HTTP error responses', () {
      final mockResponse = Response(
        data: 'Not Found',
        statusCode: 404,
        statusMessage: 'Not Found',
        requestOptions: RequestOptions(path: '/pair'),
      );

      final chipToolResponse = ChipToolResponse.fromDioResponse(mockResponse);

      expect(chipToolResponse.success, isFalse);
      expect(chipToolResponse.errorMessage, contains('HTTP 404'));
    });

    test('should recognize clear storage success message', () {
      final mockResponse = Response(
        data: {
          'returncode': 0,
          'stdout': 'Clearing Default storage\nShutting down',
          'stderr': '',
        },
        statusCode: 200,
        requestOptions: RequestOptions(path: '/command'),
      );

      final chipToolResponse = ChipToolResponse.fromDioResponse(
        mockResponse,
        operationType: 'clear',
      );

      expect(chipToolResponse.success, isTrue);
      expect(chipToolResponse.stdout, contains('Clearing Default storage'));
    });

    test('should recognize binding success patterns', () {
      final mockResponse = Response(
        data: {
          'returncode': 0,
          'stdout': 'Binding table entry added\nShutting down',
          'stderr': '',
        },
        statusCode: 200,
        requestOptions: RequestOptions(path: '/bind'),
      );

      final chipToolResponse = ChipToolResponse.fromDioResponse(
        mockResponse,
        operationType: 'binding',
      );

      expect(chipToolResponse.success, isTrue);
      expect(chipToolResponse.errorMessage, isNull);
    });

    test('should detect commission failure with returncode 1', () {
      final mockResponse = Response(
        data: {
          'returncode': 1,
          'stdout': 'Failed to establish secure session',
          'stderr': 'CHIP Error 0x00000003: Incorrect state',
        },
        statusCode: 200,
        requestOptions: RequestOptions(path: '/pair'),
      );

      final chipToolResponse = ChipToolResponse.fromDioResponse(
        mockResponse,
        operationType: 'commission',
      );

      expect(chipToolResponse.success, isFalse);
      expect(chipToolResponse.returnCode, equals(1));
      expect(chipToolResponse.errorMessage, contains('commission'));
      expect(chipToolResponse.stderr, contains('CHIP Error'));
    });
  });
}
