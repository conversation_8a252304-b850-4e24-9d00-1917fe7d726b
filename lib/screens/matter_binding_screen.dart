import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/matter_device.dart';
import '../providers/matter_binding_provider.dart';

class MatterBindingScreen extends ConsumerStatefulWidget {
  const MatterBindingScreen({super.key});

  @override
  ConsumerState<MatterBindingScreen> createState() =>
      _MatterBindingScreenState();
}

class _MatterBindingScreenState extends ConsumerState<MatterBindingScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final bindingState = ref.watch(matterBindingNotifierProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Matter Device Binding'),
        backgroundColor: Colors.blue[800],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.delete_forever),
            tooltip: 'Clear Chip Tool Storage',
            onPressed: () => _showClearStorageDialog(context, ref),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.devices), text: 'Devices'),
            Tab(icon: Icon(Icons.link), text: 'Create Binding'),
            Tab(icon: Icon(Icons.list), text: 'Bindings'),
          ],
        ),
      ),
      body: Stack(
        children: [
          TabBarView(
            controller: _tabController,
            children: const [
              _DevicesTab(),
              _CreateBindingTab(),
              _BindingsTab(),
            ],
          ),
          if (bindingState.isCommissioning || bindingState.isCreatingBinding)
            Container(
              color: Colors.black54,
              child: Center(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const CircularProgressIndicator(),
                        const SizedBox(height: 16),
                        Text(
                          bindingState.currentOperation ?? 'Processing...',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  void _showClearStorageDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Clear Chip Tool Storage'),
            content: const Text(
              'This will delete entire devices and its bindings from chip-tool controller. Still you want to continue?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () async {
                  Navigator.pop(context);

                  final success =
                      await ref
                          .read(matterBindingNotifierProvider.notifier)
                          .clearChipToolStorage();

                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          success
                              ? 'Chip tool storage cleared successfully'
                              : 'Failed to clear chip tool storage',
                        ),
                        backgroundColor: success ? Colors.green : Colors.red,
                      ),
                    );
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Clear Storage'),
              ),
            ],
          ),
    );
  }
}

class _DevicesTab extends ConsumerWidget {
  const _DevicesTab();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final devicesAsync = ref.watch(matterDevicesProvider);

    return devicesAsync.when(
      data:
          (devices) => ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: devices.length,
            itemBuilder: (context, index) {
              final device = devices[index];
              return _DeviceCard(device: device);
            },
          ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error:
          (error, stack) =>
              Center(child: Text('Error loading devices: $error')),
    );
  }
}

class _DeviceCard extends ConsumerWidget {
  final MatterDevice device;

  const _DeviceCard({required this.device});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _getDeviceIcon(device.deviceType),
                  size: 32,
                  color:
                      device.isCommissionedToChipTool
                          ? Colors.green
                          : Colors.grey,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        device.name,
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      Text(
                        device.deviceType.toUpperCase(),
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),
                if (device.isCommissionedToChipTool)
                  Chip(
                    label: Text('Node ${device.nodeId}'),
                    backgroundColor: Colors.green[100],
                  ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(
                  device.isCommissionedToChipTool
                      ? Icons.check_circle
                      : Icons.radio_button_unchecked,
                  color:
                      device.isCommissionedToChipTool
                          ? Colors.green
                          : Colors.grey,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  device.isCommissionedToChipTool
                      ? 'Commissioned to CHIPTool'
                      : 'Not commissioned to CHIPTool',
                  style: TextStyle(
                    color:
                        device.isCommissionedToChipTool
                            ? Colors.green
                            : Colors.grey,
                  ),
                ),
              ],
            ),
            if (!device.isCommissionedToChipTool) ...[
              const SizedBox(height: 12),
              ElevatedButton.icon(
                onPressed: () => _showCommissionDialog(context, ref, device),
                icon: const Icon(Icons.link),
                label: const Text('Commission to CHIPTool'),
              ),
            ] else ...[
              const SizedBox(height: 12),
              ElevatedButton.icon(
                onPressed: () => _testDeviceToggle(context, ref, device),
                icon: const Icon(Icons.toggle_on),
                label: const Text('Test Toggle'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  IconData _getDeviceIcon(String deviceType) {
    switch (deviceType) {
      case MatterDeviceTypes.light:
        return Icons.lightbulb;
      case MatterDeviceTypes.switch_:
        return Icons.toggle_on;
      case MatterDeviceTypes.dimmerSwitch:
        return Icons.tune;
      case MatterDeviceTypes.fan:
        return Icons.air;
      default:
        return Icons.device_unknown;
    }
  }

  void _showCommissionDialog(
    BuildContext context,
    WidgetRef ref,
    MatterDevice device,
  ) {
    final shareCodeController = TextEditingController();
    final nodeIdController = TextEditingController();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('Commission ${device.name}'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'Enter the share code from Home Assistant and assign a node ID:',
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: shareCodeController,
                  decoration: const InputDecoration(
                    labelText: 'Share Code',
                    hintText: 'Enter share code from HA',
                  ),
                ),
                const SizedBox(height: 12),
                TextField(
                  controller: nodeIdController,
                  decoration: const InputDecoration(
                    labelText: 'Node ID',
                    hintText: 'Enter unique node ID (e.g., 100)',
                  ),
                  keyboardType: TextInputType.number,
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () async {
                  final shareCode = shareCodeController.text.trim();
                  final nodeIdText = nodeIdController.text.trim();

                  if (shareCode.isEmpty || nodeIdText.isEmpty) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Please fill all fields')),
                    );
                    return;
                  }

                  final nodeId = int.tryParse(nodeIdText);
                  if (nodeId == null) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Invalid node ID')),
                    );
                    return;
                  }

                  Navigator.pop(context);

                  final success = await ref
                      .read(matterBindingNotifierProvider.notifier)
                      .commissionDevice(device.entityId, shareCode, nodeId);

                  if (success && context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          '${device.name} commissioned successfully',
                        ),
                      ),
                    );
                  }
                },
                child: const Text('Commission'),
              ),
            ],
          ),
    );
  }

  void _testDeviceToggle(
    BuildContext context,
    WidgetRef ref,
    MatterDevice device,
  ) async {
    if (device.nodeId == null) return;

    final success = await ref
        .read(matterBindingNotifierProvider.notifier)
        .testDeviceToggle(device.nodeId!, 1); // Using endpoint 1 as default

    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            success
                ? '${device.name} toggle test successful'
                : '${device.name} toggle test failed',
          ),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );
    }
  }
}

class _CreateBindingTab extends ConsumerStatefulWidget {
  const _CreateBindingTab();

  @override
  ConsumerState<_CreateBindingTab> createState() => _CreateBindingTabState();
}

class _CreateBindingTabState extends ConsumerState<_CreateBindingTab> {
  MatterDevice? selectedController;
  MatterDevice? selectedControlled;
  int controllerEndpoint = 1;
  int controlledEndpoint = 1;
  int clusterId = MatterClusters.onOff;

  @override
  Widget build(BuildContext context) {
    final controllerDevices = ref.watch(controllerDevicesProvider);
    final controlledDevices = ref.watch(controlledDevicesProvider);

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Create Matter Binding',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          const Text(
            'Select a controller device (switch) and a controlled device (light/fan) to create a binding.',
          ),
          const SizedBox(height: 24),

          // Controller device selection
          Text(
            'Controller Device (Switch)',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          DropdownButtonFormField<MatterDevice>(
            value: selectedController,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: 'Select controller device',
            ),
            items:
                controllerDevices.map((device) {
                  return DropdownMenuItem(
                    value: device,
                    child: Text('${device.name} (Node ${device.nodeId})'),
                  );
                }).toList(),
            onChanged: (device) {
              setState(() {
                selectedController = device;
              });
            },
          ),

          const SizedBox(height: 24),

          // Controlled device selection
          Text(
            'Controlled Device (Light/Fan)',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          DropdownButtonFormField<MatterDevice>(
            value: selectedControlled,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: 'Select controlled device',
            ),
            items:
                controlledDevices.map((device) {
                  return DropdownMenuItem(
                    value: device,
                    child: Text('${device.name} (Node ${device.nodeId})'),
                  );
                }).toList(),
            onChanged: (device) {
              setState(() {
                selectedControlled = device;
                // Auto-select appropriate cluster based on device type
                if (device?.deviceType == MatterDeviceTypes.fan) {
                  clusterId = MatterClusters.fanControl;
                } else {
                  clusterId = MatterClusters.onOff;
                }
              });
            },
          ),

          const SizedBox(height: 32),

          // Create binding button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed:
                  selectedController != null && selectedControlled != null
                      ? () => _createBinding()
                      : null,
              icon: const Icon(Icons.link),
              label: const Text('Create Binding'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.all(16),
              ),
            ),
          ),

          const SizedBox(height: 16),

          if (controllerDevices.isEmpty || controlledDevices.isEmpty)
            Card(
              color: Colors.orange[50],
              child: const Padding(
                padding: EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(Icons.warning, color: Colors.orange),
                    SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'You need at least one controller and one controlled device commissioned to CHIPTool to create bindings.',
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Future<void> _createBinding() async {
    if (selectedController == null || selectedControlled == null) return;

    final success = await ref
        .read(matterBindingNotifierProvider.notifier)
        .createBinding(
          selectedController!,
          selectedControlled!,
          controllerEndpoint,
          controlledEndpoint,
          clusterId,
        );

    if (success) {
      setState(() {
        selectedController = null;
        selectedControlled = null;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Binding created successfully!')),
        );
      }
    }
  }
}

class _BindingsTab extends ConsumerWidget {
  const _BindingsTab();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final bindingsAsync = ref.watch(matterBindingsProvider);

    return bindingsAsync.when(
      data: (bindings) {
        if (bindings.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.link_off, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  'No bindings created yet',
                  style: TextStyle(fontSize: 18, color: Colors.grey),
                ),
                SizedBox(height: 8),
                Text(
                  'Create bindings in the "Create Binding" tab',
                  style: TextStyle(color: Colors.grey),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: bindings.length,
          itemBuilder: (context, index) {
            final binding = bindings[index];
            return _BindingCard(binding: binding);
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error:
          (error, stack) =>
              Center(child: Text('Error loading bindings: $error')),
    );
  }
}

class _BindingCard extends ConsumerWidget {
  final MatterBinding binding;

  const _BindingCard({required this.binding});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${binding.controllerDevice.name} → ${binding.controlledDevice.name}',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Cluster: ${_getClusterName(binding.clusterId)}',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                      Text(
                        'Created: ${_formatDate(binding.createdAt)}',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => _removeBinding(context, ref),
                  icon: const Icon(Icons.delete, color: Colors.red),
                  tooltip: 'Remove binding',
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(
                  binding.isActive ? Icons.check_circle : Icons.error,
                  color: binding.isActive ? Colors.green : Colors.red,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  binding.isActive ? 'Active' : 'Inactive',
                  style: TextStyle(
                    color: binding.isActive ? Colors.green : Colors.red,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _getClusterName(int clusterId) {
    switch (clusterId) {
      case MatterClusters.onOff:
        return 'On/Off';
      case MatterClusters.levelControl:
        return 'Level Control';
      case MatterClusters.fanControl:
        return 'Fan Control';
      default:
        return 'Unknown ($clusterId)';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  Future<void> _removeBinding(BuildContext context, WidgetRef ref) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Remove Binding'),
            content: Text(
              'Are you sure you want to remove the binding between ${binding.controllerDevice.name} and ${binding.controlledDevice.name}?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context, true),
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                child: const Text('Remove'),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      final success = await ref
          .read(matterBindingNotifierProvider.notifier)
          .removeBinding(binding.id);

      if (success && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Binding removed successfully')),
        );
      }
    }
  }
}
