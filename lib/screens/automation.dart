import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

// Import the EntityInfo class from scene.dart
import 'scene.dart';

class AutomationTrigger {
  final String type; // 'state', 'time', 'device'
  final String? entityId;
  final String? fromState;
  final String? toState;
  final String? platform;
  final Map<String, dynamic> data;

  AutomationTrigger({
    required this.type,
    this.entityId,
    this.fromState,
    this.toState,
    this.platform,
    this.data = const {},
  });

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{'platform': platform ?? type};

    if (entityId != null) json['entity_id'] = entityId;
    if (fromState != null) json['from'] = fromState;
    if (toState != null) json['to'] = toState;

    json.addAll(data);
    return json;
  }

  factory AutomationTrigger.fromJson(Map<String, dynamic> json) {
    return AutomationTrigger(
      type: json['platform'] ?? 'state',
      entityId: json['entity_id'],
      fromState: json['from'],
      toState: json['to'],
      platform: json['platform'],
      data: Map<String, dynamic>.from(json)..removeWhere(
        (key, value) => ['platform', 'entity_id', 'from', 'to'].contains(key),
      ),
    );
  }
}

class AutomationCondition {
  final String type; // 'state', 'time', 'template'
  final String? entityId;
  final String? state;
  final Map<String, dynamic> data;

  AutomationCondition({
    required this.type,
    this.entityId,
    this.state,
    this.data = const {},
  });

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{'condition': type};

    if (entityId != null) json['entity_id'] = entityId;
    if (state != null) json['state'] = state;

    json.addAll(data);
    return json;
  }

  factory AutomationCondition.fromJson(Map<String, dynamic> json) {
    return AutomationCondition(
      type: json['condition'] ?? 'state',
      entityId: json['entity_id'],
      state: json['state'],
      data: Map<String, dynamic>.from(json)..removeWhere(
        (key, value) => ['condition', 'entity_id', 'state'].contains(key),
      ),
    );
  }
}

class AutomationAction {
  final String type; // 'call_service', 'scene'
  final String? service;
  final String? entityId;
  final Map<String, dynamic> data;

  AutomationAction({
    required this.type,
    this.service,
    this.entityId,
    this.data = const {},
  });

  Map<String, dynamic> toJson() {
    if (type == 'scene') {
      return {'scene': entityId};
    } else {
      final json = <String, dynamic>{'service': service};

      if (entityId != null) {
        json['target'] = {'entity_id': entityId};
      }

      if (data.isNotEmpty) {
        json['data'] = data;
      }

      return json;
    }
  }

  factory AutomationAction.fromJson(Map<String, dynamic> json) {
    if (json.containsKey('scene')) {
      return AutomationAction(type: 'scene', entityId: json['scene']);
    } else {
      return AutomationAction(
        type: 'call_service',
        service: json['service'],
        entityId: json['target']?['entity_id'],
        data: json['data'] ?? {},
      );
    }
  }
}

class Automation {
  final String id;
  final String alias;
  final String description;
  final List<AutomationTrigger> triggers;
  final List<AutomationCondition> conditions;
  final List<AutomationAction> actions;
  final String mode;
  final bool enabled;

  Automation({
    required this.id,
    required this.alias,
    this.description = '',
    required this.triggers,
    this.conditions = const [],
    required this.actions,
    this.mode = 'single',
    this.enabled = true,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'alias': alias,
      'description': description,
      'trigger': triggers.map((t) => t.toJson()).toList(),
      'condition': conditions.map((c) => c.toJson()).toList(),
      'action': actions.map((a) => a.toJson()).toList(),
      'mode': mode,
    };
  }

  factory Automation.fromJson(Map<String, dynamic> json) {
    return Automation(
      id: json['id'] ?? '',
      alias: json['alias'] ?? '',
      description: json['description'] ?? '',
      triggers:
          (json['trigger'] as List<dynamic>?)
              ?.map((t) => AutomationTrigger.fromJson(t))
              .toList() ??
          [],
      conditions:
          (json['condition'] as List<dynamic>?)
              ?.map((c) => AutomationCondition.fromJson(c))
              .toList() ??
          [],
      actions:
          (json['action'] as List<dynamic>?)
              ?.map((a) => AutomationAction.fromJson(a))
              .toList() ??
          [],
      mode: json['mode'] ?? 'single',
      enabled: json['enabled'] ?? true,
    );
  }
}

class AutomationApiService {
  static const String baseUrl = 'http://*************:8123/api';
  static const String automationConfigUrl = '$baseUrl/config/automation/config';
  static const String authToken =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiI5ZjVlM2QyNjQ4ZjQ0NDAwOWE0NWExNjJlMDAwZmRjZSIsImlhdCI6MTczODgxODgwNSwiZXhwIjoyMDU0MTc4ODA1fQ.vOEYd3rL0poOHWUOkf0_QTwxpkzDTtubCzpvlb9zWcY';

  static Map<String, String> get headers => {
    'Authorization': 'Bearer $authToken',
    'Content-Type': 'application/json',
  };

  static Future<bool> createAutomation(Automation automation) async {
    try {
      final automationId =
          automation.id.isEmpty
              ? DateTime.now().millisecondsSinceEpoch.toString()
              : automation.id;
      final url = '$automationConfigUrl/$automationId';

      final response = await http.post(
        Uri.parse(url),
        headers: headers,
        body: json.encode(automation.toJson()),
      );

      return response.statusCode == 200;
    } catch (e) {
      log('Error creating automation: $e');
      return false;
    }
  }

  static Future<Automation?> getAutomation(String automationId) async {
    try {
      final url = '$automationConfigUrl/$automationId';
      final response = await http.get(Uri.parse(url), headers: headers);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return Automation.fromJson(data);
      }
      return null;
    } catch (e) {
      log('Error fetching automation: $e');
      return null;
    }
  }

  static Future<List<Automation>> getAllAutomations() async {
    // Similar to scenes, we'll simulate this for now
    // In a real implementation, you'd store automation IDs locally
    List<Automation> automations = [];

    List<String> knownAutomationIds = [
      // Add your known automation IDs here
    ];

    for (String id in knownAutomationIds) {
      Automation? automation = await getAutomation(id);
      if (automation != null) {
        automations.add(automation);
      }
    }

    return automations;
  }

  // Get all scenes for action selection
  static Future<List<Scene>> getAllScenes() async {
    return await ApiService.getAllScenes();
  }
}

class AutomationListPage extends StatefulWidget {
  const AutomationListPage({super.key});

  @override
  AutomationListPageState createState() => AutomationListPageState();
}

class AutomationListPageState extends State<AutomationListPage> {
  List<Automation> automations = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    loadAutomations();
  }

  Future<void> loadAutomations() async {
    setState(() => isLoading = true);
    automations = await AutomationApiService.getAllAutomations();
    setState(() => isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Automations'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body:
          isLoading
              ? Center(child: CircularProgressIndicator(color: Colors.blue))
              : RefreshIndicator(
                onRefresh: loadAutomations,
                color: Colors.blue,
                child:
                    automations.isEmpty
                        ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.auto_awesome,
                                size: 64,
                                color: Colors.blue,
                              ),
                              SizedBox(height: 16),
                              Text(
                                'No automations found',
                                style: TextStyle(
                                  fontSize: 18,
                                  color: Colors.blue,
                                ),
                              ),
                              SizedBox(height: 8),
                              Text(
                                'Pull to refresh or create a new automation',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.blue.withValues(alpha: 0.7),
                                ),
                              ),
                            ],
                          ),
                        )
                        : ListView.builder(
                          itemCount: automations.length,
                          itemBuilder: (context, index) {
                            final automation = automations[index];
                            return Card(
                              margin: EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 8,
                              ),
                              child: ListTile(
                                leading: CircleAvatar(
                                  backgroundColor:
                                      automation.enabled
                                          ? Colors.blue
                                          : Colors.grey,
                                  child: Icon(
                                    Icons.auto_awesome,
                                    color: Colors.white,
                                  ),
                                ),
                                title: Text(
                                  automation.alias,
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                ),
                                subtitle: Text(
                                  '${automation.triggers.length} triggers, ${automation.actions.length} actions',
                                  style: TextStyle(
                                    color: Colors.blue.withValues(alpha: 0.7),
                                  ),
                                ),
                                trailing: Icon(
                                  Icons.arrow_forward_ios,
                                  color: Colors.blue,
                                ),
                                onTap: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder:
                                          (context) => AutomationDetailPage(
                                            automation: automation,
                                          ),
                                    ),
                                  );
                                },
                              ),
                            );
                          },
                        ),
              ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => CreateAutomationPage()),
          );
          if (result == true) {
            loadAutomations();
          }
        },
        backgroundColor: Colors.blue,
        child: Icon(Icons.add, color: Colors.white),
      ),
    );
  }
}

class CreateAutomationPage extends StatefulWidget {
  const CreateAutomationPage({super.key});

  @override
  CreateAutomationPageState createState() => CreateAutomationPageState();
}

class CreateAutomationPageState extends State<CreateAutomationPage> {
  final _formKey = GlobalKey<FormState>();
  final _aliasController = TextEditingController();
  final _descriptionController = TextEditingController();

  List<EntityInfo> _entities = [];
  List<Scene> _scenes = [];
  bool _isLoadingData = true;
  bool _isCreating = false;

  // Automation components
  AutomationTrigger? _trigger;
  AutomationCondition? _condition;
  AutomationAction? _action;

  // Action configuration
  String? _selectedActionType;
  EntityInfo? _selectedActionEntity;
  String _deviceAction = 'turn_on';
  Map<String, dynamic> _actionData = {};

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoadingData = true);
    final entities = await ApiService.getAllEntities();
    final scenes = await AutomationApiService.getAllScenes();
    setState(() {
      _entities =
          entities
              .where(
                (entity) =>
                    entity.domain == 'light' ||
                    entity.domain == 'fan' ||
                    entity.domain == 'switch',
              )
              .toList();
      _scenes = scenes;
      _isLoadingData = false;
    });
  }

  Future<void> _createAutomation() async {
    if (!_formKey.currentState!.validate() ||
        _trigger == null ||
        _action == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Please fill in all required fields'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() => _isCreating = true);

    try {
      final automation = Automation(
        id: '',
        alias: _aliasController.text,
        description: _descriptionController.text,
        triggers: [_trigger!],
        conditions: _condition != null ? [_condition!] : [],
        actions: [_action!],
      );

      final success = await AutomationApiService.createAutomation(automation);

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Automation created successfully!'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context, true);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to create automation'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isCreating = false);
      }
    }
  }

  @override
  void dispose() {
    _aliasController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Create Automation'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body:
          _isLoadingData
              ? Center(child: CircularProgressIndicator(color: Colors.blue))
              : Form(
                key: _formKey,
                child: ListView(
                  padding: EdgeInsets.all(16),
                  children: [
                    TextFormField(
                      controller: _aliasController,
                      decoration: InputDecoration(
                        labelText: 'Automation Name',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(
                          Icons.auto_awesome,
                          color: Colors.blue,
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter an automation name';
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: 16),
                    TextFormField(
                      controller: _descriptionController,
                      decoration: InputDecoration(
                        labelText: 'Description (Optional)',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.description, color: Colors.blue),
                      ),
                      maxLines: 2,
                    ),
                    SizedBox(height: 24),
                    _buildTriggerSection(),
                    SizedBox(height: 24),
                    _buildConditionSection(),
                    SizedBox(height: 24),
                    _buildActionSection(),
                    SizedBox(height: 32),
                    ElevatedButton(
                      onPressed: _isCreating ? null : _createAutomation,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        padding: EdgeInsets.symmetric(vertical: 16),
                      ),
                      child:
                          _isCreating
                              ? Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white,
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 8),
                                  Text(
                                    'Creating...',
                                    style: TextStyle(color: Colors.white),
                                  ),
                                ],
                              )
                              : Text(
                                'Create Automation',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.white,
                                ),
                              ),
                    ),
                  ],
                ),
              ),
    );
  }

  Widget _buildTriggerSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'When (Trigger)',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            SizedBox(height: 16),
            DropdownButtonFormField<EntityInfo>(
              decoration: InputDecoration(
                labelText: 'Select Device',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.device_hub, color: Colors.blue),
              ),
              items:
                  _entities.map((entity) {
                    return DropdownMenuItem(
                      value: entity,
                      child: Text(
                        '${entity.friendlyName} (${entity.entityId})',
                      ),
                    );
                  }).toList(),
              onChanged: (entity) {
                if (entity != null) {
                  setState(() {
                    _trigger = AutomationTrigger(
                      type: 'state',
                      platform: 'state',
                      entityId: entity.entityId,
                      toState: 'on',
                    );
                  });
                }
              },
            ),
            if (_trigger != null) ...[
              SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _trigger!.toState,
                decoration: InputDecoration(
                  labelText: 'To State',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(
                    Icons.power_settings_new,
                    color: Colors.blue,
                  ),
                ),
                items:
                    ['on', 'off'].map((state) {
                      return DropdownMenuItem(
                        value: state,
                        child: Text(state.toUpperCase()),
                      );
                    }).toList(),
                onChanged: (state) {
                  if (state != null) {
                    setState(() {
                      _trigger = AutomationTrigger(
                        type: 'state',
                        platform: 'state',
                        entityId: _trigger!.entityId,
                        toState: state,
                      );
                    });
                  }
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildConditionSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'And If (Condition - Optional)',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            SizedBox(height: 16),
            Text(
              'Conditions are optional. Leave empty to run the automation whenever the trigger occurs.',
              style: TextStyle(
                color: Colors.blue.withValues(alpha: 0.7),
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Then Do (Action)',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
            SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _selectedActionType,
              decoration: InputDecoration(
                labelText: 'Action Type',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.play_arrow, color: Colors.blue),
              ),
              items: [
                DropdownMenuItem(
                  value: 'device',
                  child: Text('Control Device'),
                ),
                DropdownMenuItem(value: 'scene', child: Text('Activate Scene')),
              ],
              onChanged: (type) {
                setState(() {
                  _selectedActionType = type;
                  _selectedActionEntity = null;
                  _deviceAction = 'turn_on';
                  _actionData = {};
                  _action = null;
                });
              },
            ),
            if (_selectedActionType == 'device') ...[
              SizedBox(height: 16),
              DropdownButtonFormField<EntityInfo>(
                value: _selectedActionEntity,
                decoration: InputDecoration(
                  labelText: 'Select Device',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.device_hub, color: Colors.blue),
                ),
                items:
                    _entities.map((entity) {
                      return DropdownMenuItem(
                        value: entity,
                        child: Text(entity.friendlyName),
                      );
                    }).toList(),
                onChanged: (entity) {
                  setState(() {
                    _selectedActionEntity = entity;
                    if (entity != null) {
                      _deviceAction = 'turn_on';
                      _actionData = {};
                      _updateAction();
                    }
                  });
                },
              ),
              if (_selectedActionEntity != null) ...[
                SizedBox(height: 16),
                _buildDeviceActionControls(),
              ],
            ],
            if (_selectedActionType == 'scene') ...[
              SizedBox(height: 16),
              DropdownButtonFormField<Scene>(
                decoration: InputDecoration(
                  labelText: 'Select Scene',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.palette, color: Colors.blue),
                ),
                items:
                    _scenes.map((scene) {
                      return DropdownMenuItem(
                        value: scene,
                        child: Text(scene.name),
                      );
                    }).toList(),
                onChanged: (scene) {
                  if (scene != null) {
                    setState(() {
                      _action = AutomationAction(
                        type: 'scene',
                        entityId: 'scene.${scene.id}',
                      );
                    });
                  }
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDeviceActionControls() {
    if (_selectedActionEntity == null) return SizedBox.shrink();

    final entity = _selectedActionEntity!;
    final controls = <Widget>[];

    // Action type dropdown
    controls.add(
      DropdownButtonFormField<String>(
        value: _deviceAction,
        decoration: InputDecoration(
          labelText: 'Action',
          border: OutlineInputBorder(),
          prefixIcon: Icon(Icons.settings, color: Colors.blue),
        ),
        items: _getDeviceActions(entity.domain),
        onChanged: (action) {
          setState(() {
            _deviceAction = action!;
            _actionData = {};
            _updateAction();
          });
        },
      ),
    );

    // Domain-specific controls
    if (_deviceAction == 'turn_on') {
      switch (entity.domain) {
        case 'light':
          controls.add(SizedBox(height: 16));
          controls.add(_buildLightControls());
          break;
        case 'fan':
          controls.add(SizedBox(height: 16));
          controls.add(_buildFanControls());
          break;
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: controls,
    );
  }

  List<DropdownMenuItem<String>> _getDeviceActions(String domain) {
    switch (domain) {
      case 'light':
        return [
          DropdownMenuItem(value: 'turn_on', child: Text('Turn On')),
          DropdownMenuItem(value: 'turn_off', child: Text('Turn Off')),
          DropdownMenuItem(value: 'toggle', child: Text('Toggle')),
        ];
      case 'fan':
        return [
          DropdownMenuItem(value: 'turn_on', child: Text('Turn On')),
          DropdownMenuItem(value: 'turn_off', child: Text('Turn Off')),
          DropdownMenuItem(value: 'toggle', child: Text('Toggle')),
          DropdownMenuItem(value: 'set_preset_mode', child: Text('Set Speed')),
        ];
      case 'switch':
        return [
          DropdownMenuItem(value: 'turn_on', child: Text('Turn On')),
          DropdownMenuItem(value: 'turn_off', child: Text('Turn Off')),
          DropdownMenuItem(value: 'toggle', child: Text('Toggle')),
        ];
      default:
        return [
          DropdownMenuItem(value: 'turn_on', child: Text('Turn On')),
          DropdownMenuItem(value: 'turn_off', child: Text('Turn Off')),
        ];
    }
  }

  Widget _buildLightControls() {
    return TextFormField(
      initialValue: _actionData['brightness']?.toString() ?? '255',
      decoration: InputDecoration(
        labelText: 'Brightness (0-255)',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.brightness_6, color: Colors.blue),
      ),
      keyboardType: TextInputType.number,
      onChanged: (value) {
        final brightness = int.tryParse(value);
        if (brightness != null && brightness >= 0 && brightness <= 255) {
          setState(() {
            _actionData['brightness'] = brightness;
            _updateAction();
          });
        }
      },
    );
  }

  Widget _buildFanControls() {
    return DropdownButtonFormField<String>(
      value: _actionData['preset_mode']?.toString() ?? 'low',
      decoration: InputDecoration(
        labelText: 'Speed',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.air, color: Colors.blue),
      ),
      items:
          ['low', 'medium', 'high', 'auto'].map((mode) {
            return DropdownMenuItem(
              value: mode,
              child: Text(mode.toUpperCase()),
            );
          }).toList(),
      onChanged: (mode) {
        setState(() {
          _actionData['preset_mode'] = mode!;
          _updateAction();
        });
      },
    );
  }

  void _updateAction() {
    if (_selectedActionEntity == null) return;

    final entity = _selectedActionEntity!;
    setState(() {
      _action = AutomationAction(
        type: 'call_service',
        service: '${entity.domain}.$_deviceAction',
        entityId: entity.entityId,
        data: _actionData,
      );
    });
  }
}

class AutomationDetailPage extends StatelessWidget {
  final Automation automation;

  const AutomationDetailPage({super.key, required this.automation});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(automation.alias),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: ListView(
        padding: EdgeInsets.all(16),
        children: [
          Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info, color: Colors.blue),
                      SizedBox(width: 8),
                      Text(
                        'Automation Information',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  _buildInfoRow('Name', automation.alias),
                  _buildInfoRow(
                    'Description',
                    automation.description.isEmpty
                        ? 'No description'
                        : automation.description,
                  ),
                  _buildInfoRow('Mode', automation.mode),
                  _buildInfoRow(
                    'Status',
                    automation.enabled ? 'Enabled' : 'Disabled',
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: 16),
          Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.play_arrow, color: Colors.blue),
                      SizedBox(width: 8),
                      Text(
                        'Triggers (${automation.triggers.length})',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  ...automation.triggers.map((trigger) {
                    return _buildTriggerCard(trigger);
                  }),
                ],
              ),
            ),
          ),
          if (automation.conditions.isNotEmpty) ...[
            SizedBox(height: 16),
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.rule, color: Colors.blue),
                        SizedBox(width: 8),
                        Text(
                          'Conditions (${automation.conditions.length})',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 16),
                    ...automation.conditions.map((condition) {
                      return _buildConditionCard(condition);
                    }),
                  ],
                ),
              ),
            ),
          ],
          SizedBox(height: 16),
          Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.settings, color: Colors.blue),
                      SizedBox(width: 8),
                      Text(
                        'Actions (${automation.actions.length})',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  ...automation.actions.map((action) {
                    return _buildActionCard(action);
                  }),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.blue.withValues(alpha: 0.7),
              ),
            ),
          ),
          Expanded(child: Text(value, style: TextStyle(fontSize: 16))),
        ],
      ),
    );
  }

  Widget _buildTriggerCard(AutomationTrigger trigger) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'State Change Trigger',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: Colors.blue,
            ),
          ),
          SizedBox(height: 8),
          if (trigger.entityId != null) Text('Entity: ${trigger.entityId}'),
          if (trigger.toState != null) Text('To State: ${trigger.toState}'),
        ],
      ),
    );
  }

  Widget _buildConditionCard(AutomationCondition condition) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Condition',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: Colors.blue,
            ),
          ),
          SizedBox(height: 8),
          Text('Type: ${condition.type}'),
          if (condition.entityId != null) Text('Entity: ${condition.entityId}'),
          if (condition.state != null) Text('State: ${condition.state}'),
        ],
      ),
    );
  }

  Widget _buildActionCard(AutomationAction action) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            action.type == 'scene' ? 'Activate Scene' : 'Call Service',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: Colors.blue,
            ),
          ),
          SizedBox(height: 8),
          if (action.type == 'scene')
            Text('Scene: ${action.entityId}')
          else ...[
            if (action.service != null) Text('Service: ${action.service}'),
            if (action.entityId != null) Text('Entity: ${action.entityId}'),
          ],
        ],
      ),
    );
  }
}
