import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'screens/virtual_dock_screen.dart';
import 'services/connectivity_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Initialize only essential services
    // Removed Firebase initialization and analytics services

    // Initialize connectivity service
    final connectivityService = ConnectivityService();
    await connectivityService.initialize();

    log('App initialized with essential services only');
  } catch (e) {
    log('Error initializing app: $e');
  }

  runApp(
    const ProviderScope(
      child: MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'Schnell Smart Home',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.dark,
        ),
        useMaterial3: true,
      ),
      darkTheme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.dark,
        ),
        useMaterial3: true,
      ),
      themeMode: ThemeMode.dark,
      home: const VirtualDockScreen(),
    );
  }
}

i have paired a new device via binding screen device tab FAB using share code(or pair code), it shows the device added successfully toast also, but after i tried toggle from postman client its shows the timeout error. each and every when i tried to add the device from this app the device was not paired. but the same was worked from postman. you need check the code base and resolve this erros.
the below was i have tried from postman client and it was worked as expected.

Step 1: Pair Device(Switch):

curl --location 'http://homeassistant.local:6000/pair' \
--header 'Content-Type: application/json' \
--data '{"node_id": "1", "passcode": "12988108191"}'

Step 2: Check device(Switch) using toggle:

curl --location 'http://homeassistant.local:6000/toggle' \
--header 'Content-Type: application/json' \
--data '{"node_id": "1", "endpoint_id": "1"}'

Step 3: Pair Device(Light):

curl --location 'http://homeassistant.local:6000/pair' \
--header 'Content-Type: application/json' \
--data '{"node_id": "2", "passcode": "5879108191"}'

Step 2: Check device(Light) using toggle:

curl --location 'http://homeassistant.local:6000/toggle' \
--header 'Content-Type: application/json' \
--data '{"node_id": "2", "endpoint_id": "1"}'

Step 4: Create Binding:

curl --location 'http://homeassistant.local:6000/bind' \
--header 'Content-Type: application/json' \
--data '{
  "switch_node": 1,
  "switch_endpoint": 1,
  "light_node": 2,
  "light_endpoint": 1
}'

Step 5: Check device(Light) turned on when pressed the button from the device(Switch)

I have verified all the above steps are worked in the postman client. but after implementing the same in this flutter project the pairing and binding was not worked. if you can use the ip instead of url to achieve this (ex: http://*************:6000/toggle). I need the complete working project.