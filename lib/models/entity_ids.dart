// Class to hold all entity IDs used in the application
class EntityIds {
  final String power1;
  final String power2;
  final String light;
  final String fan;
  final String favourite1;
  final String favourite2;

  const EntityIds({
    this.power1 = "switch.test_product_switch_1",
    this.power2 = "switch.test_product_switch_2",
    this.light = "light.test_product_8",
    this.fan = "fan.test_product",
    this.favourite1 = "scene.turn_off",
    this.favourite2 = "scene.turn_off_all_devices_duplicate",
  });

  // Default instance for easy access
  static const EntityIds defaultIds = EntityIds();
}
