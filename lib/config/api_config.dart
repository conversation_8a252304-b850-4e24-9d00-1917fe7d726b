import 'package:dio/dio.dart';

/// API configuration for Matter binding services using Dio
class ApiConfig {
  // Change this IP address to match your Home Assistant server
  // You can find your Home Assistant IP by going to Settings > System > Network
  static const String homeAssistantIp = '*************';
  static const int chipToolPort = 6000;

  // Base URL for chip-tool API
  static String get chipToolBaseUrl => 'http://$homeAssistantIp:$chipToolPort';

  // API endpoints
  static String get pairUrl => '$chipToolBaseUrl/pair';
  static String get bindUrl => '$chipToolBaseUrl/bind';
  static String get toggleUrl => '$chipToolBaseUrl/toggle';
  static String get commandUrl => '$chipToolBaseUrl/command';

  // Request timeout duration
  static const Duration requestTimeout = Duration(seconds: 30);

  // Common headers for Dio
  static const Map<String, String> headers = {
    'Content-Type': 'application/json',
  };

  // Get configured Dio instance
  static Dio getDio() {
    final dio = Dio();
    dio.options.connectTimeout = requestTimeout;
    dio.options.receiveTimeout = requestTimeout;
    dio.options.sendTimeout = requestTimeout;
    return dio;
  }
}
