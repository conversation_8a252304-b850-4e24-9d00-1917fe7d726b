import 'dart:convert';
import 'dart:developer';
import 'package:http/http.dart' as http;
import 'binding_storage_service.dart';
import '../config/api_config.dart';

class NewBindingApiService {
  static String get pairUrl => ApiConfig.pairUrl;
  static String get bindUrl => ApiConfig.bindUrl;

  final BindingStorageService _storageService = BindingStorageService();

  // Pair a device using passcode and auto-generated node ID
  Future<PairedDevice?> pairDevice({
    required String deviceName,
    required String deviceType,
    required String passcode,
  }) async {
    try {
      // Get next available node ID
      final nodeId = await _storageService.getNextNodeId();

      return await pairDeviceWithNodeId(
        deviceName: deviceName,
        deviceType: deviceType,
        passcode: passcode,
        nodeId: nodeId,
      );
    } catch (e) {
      log('Error pairing device: $e');
      return null;
    }
  }

  // Pair a device using passcode and manual node ID
  Future<PairedDevice?> pairDeviceWithNodeId({
    required String deviceName,
    required String deviceType,
    required String passcode,
    required int nodeId,
  }) async {
    try {
      log('Pairing device: $deviceName with node ID: $nodeId');

      final response = await http.post(
        Uri.parse(pairUrl),
            headers: ApiConfig.headers,
            body: json.encode({'node_id': nodeId, 'passcode': passcode}),
          )
          .timeout(ApiConfig.requestTimeout);

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        log('Pairing response: $responseData');

        // Create paired device object
        final pairedDevice = PairedDevice(
          name: deviceName,
          deviceType: deviceType,
          nodeId: nodeId,
          passcode: passcode,
          pairedAt: DateTime.now(),
          endpoints: [1], // Default endpoint, can be customized later
        );

        // Save to local storage
        await _storageService.addPairedDevice(pairedDevice);

        log(
          'Device paired successfully: ${pairedDevice.name} (Node: ${pairedDevice.nodeId})',
        );
        return pairedDevice;
      } else {
        log('Pairing failed with status: ${response.statusCode}');
        log('Response body: ${response.body}');
        return null;
      }
    } catch (e) {
      log('Error pairing device: $e');
      return null;
    }
  }

  // Create binding between two devices
  Future<DeviceBinding?> createBinding({
    required int sourceNodeId,
    required int sourceEndpoint,
    required int targetNodeId,
    required int targetEndpoint,
  }) async {
    try {
      log(
        'Creating binding: Source($sourceNodeId:$sourceEndpoint) -> Target($targetNodeId:$targetEndpoint)',
      );

      final response = await http.post(
        Uri.parse(bindUrl),
            headers: ApiConfig.headers,
        body: json.encode({
          'switch_node': sourceNodeId,
          'switch_endpoint': sourceEndpoint,
          'light_node': targetNodeId,
          'light_endpoint': targetEndpoint,
        }),
          )
          .timeout(ApiConfig.requestTimeout);

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        log('Binding response: $responseData');

        // Get device names for the binding
        final sourceDevice = await _storageService.getDeviceByNodeId(
          sourceNodeId,
        );
        final targetDevice = await _storageService.getDeviceByNodeId(
          targetNodeId,
        );

        // Create binding object
        final binding = DeviceBinding(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          sourceNodeId: sourceNodeId,
          sourceEndpoint: sourceEndpoint,
          targetNodeId: targetNodeId,
          targetEndpoint: targetEndpoint,
          sourceName: sourceDevice?.name ?? 'Unknown Device',
          targetName: targetDevice?.name ?? 'Unknown Device',
          createdAt: DateTime.now(),
        );

        // Save to local storage
        await _storageService.addDeviceBinding(binding);

        log(
          'Binding created successfully: ${binding.sourceName} -> ${binding.targetName}',
        );
        return binding;
      } else {
        log('Binding failed with status: ${response.statusCode}');
        log('Response body: ${response.body}');
        return null;
      }
    } catch (e) {
      log('Error creating binding: $e');
      return null;
    }
  }

  // Get all paired devices
  Future<List<PairedDevice>> getAllPairedDevices() async {
    return await _storageService.loadPairedDevices();
  }

  // Get all device bindings
  Future<List<DeviceBinding>> getAllBindings() async {
    return await _storageService.loadDeviceBindings();
  }

  // Remove a paired device
  Future<bool> removePairedDevice(int nodeId) async {
    try {
      await _storageService.removePairedDevice(nodeId);

      // Also remove any bindings involving this device
      final bindings = await _storageService.loadDeviceBindings();
      final bindingsToRemove =
          bindings
              .where(
                (b) => b.sourceNodeId == nodeId || b.targetNodeId == nodeId,
              )
              .toList();

      for (final binding in bindingsToRemove) {
        await _storageService.removeDeviceBinding(binding.id);
      }

      return true;
    } catch (e) {
      log('Error removing paired device: $e');
      return false;
    }
  }

  // Remove a device binding
  Future<bool> removeBinding(String bindingId) async {
    try {
      await _storageService.removeDeviceBinding(bindingId);
      return true;
    } catch (e) {
      log('Error removing binding: $e');
      return false;
    }
  }

  // Get next available node ID
  Future<int> getNextNodeId() async {
    return await _storageService.getNextNodeId();
  }

  // Check if node ID is available
  Future<bool> isNodeIdAvailable(int nodeId) async {
    return !(await _storageService.isNodeIdUsed(nodeId));
  }

  // Check if node ID is already used
  Future<bool> isNodeIdUsed(int nodeId) async {
    return await _storageService.isNodeIdUsed(nodeId);
  }

  // Get device by node ID
  Future<PairedDevice?> getDeviceByNodeId(int nodeId) async {
    return await _storageService.getDeviceByNodeId(nodeId);
  }

  // Clear all data (for testing)
  Future<void> clearAllData() async {
    await _storageService.clearAllData();
  }

  // Test connection to binding service
  Future<bool> testConnection() async {
    try {
      final response = await http.get(Uri.parse(ApiConfig.chipToolBaseUrl));
      return response.statusCode == 200;
    } catch (e) {
      log('Connection test failed: $e');
      return false;
    }
  }

  // Get binding statistics
  Future<Map<String, int>> getBindingStats() async {
    final devices = await getAllPairedDevices();
    final bindings = await getAllBindings();

    return {
      'totalDevices': devices.length,
      'totalBindings': bindings.length,
      'switchDevices':
          devices
              .where((d) => d.deviceType.toLowerCase().contains('switch'))
              .length,
      'lightDevices':
          devices
              .where((d) => d.deviceType.toLowerCase().contains('light'))
              .length,
      'fanDevices':
          devices
              .where((d) => d.deviceType.toLowerCase().contains('fan'))
              .length,
    };
  }

  // Validate binding configuration
  bool validateBinding({
    required int sourceNodeId,
    required int sourceEndpoint,
    required int targetNodeId,
    required int targetEndpoint,
  }) {
    // Basic validation
    if (sourceNodeId <= 0 || targetNodeId <= 0) {
      return false;
    }

    if (sourceEndpoint <= 0 || targetEndpoint <= 0) {
      return false;
    }

    if (sourceNodeId == targetNodeId) {
      return false; // Can't bind device to itself
    }

    return true;
  }

  // Get available endpoints for a device type
  List<int> getAvailableEndpoints(String deviceType) {
    switch (deviceType.toLowerCase()) {
      case 'switch':
        return [1]; // Switches typically have endpoint 1
      case 'light':
        return [1]; // Lights typically have endpoint 1
      case 'fan':
        return [1]; // Fans typically have endpoint 1
      default:
        return [1]; // Default to endpoint 1
    }
  }

  // Get recommended device types for pairing
  List<String> getDeviceTypes() {
    return ['Switch', 'Light', 'Fan', 'Sensor', 'Outlet', 'Dimmer'];
  }
}
