import 'package:flutter/material.dart';
import '../services/new_binding_api_service.dart';
import 'api_debug_helper.dart';

/// Debug screen to test device pairing with detailed logging
class DebugPairingScreen extends StatefulWidget {
  const DebugPairingScreen({super.key});

  @override
  State<DebugPairingScreen> createState() => _DebugPairingScreenState();
}

class _DebugPairingScreenState extends State<DebugPairingScreen> {
  final _passcodeController = TextEditingController();
  final _nodeIdController = TextEditingController(text: '1');
  final _apiService = NewBindingApiService();
  bool _isLoading = false;
  String _lastResult = '';

  @override
  void dispose() {
    _passcodeController.dispose();
    _nodeIdController.dispose();
    super.dispose();
  }

  Future<void> _testPairing() async {
    final passcode = _passcodeController.text.trim();
    final nodeIdText = _nodeIdController.text.trim();
    
    if (passcode.isEmpty || nodeIdText.isEmpty) {
      _showResult('Please enter both passcode and node ID');
      return;
    }
    
    final nodeId = int.tryParse(nodeIdText);
    if (nodeId == null) {
      _showResult('Invalid node ID');
      return;
    }
    
    setState(() {
      _isLoading = true;
      _lastResult = 'Testing pairing...';
    });
    
    try {
      // Log comparison with Postman format
      ApiDebugHelper.logPostmanComparison(
        passcode: passcode,
        nodeId: nodeId,
      );
      
      // Test with debug helper
      await ApiDebugHelper.testDevicePairing(
        passcode: passcode,
        nodeId: nodeId,
      );
      
      // Test with actual service
      final result = await _apiService.pairDeviceWithNodeId(
        deviceName: 'Debug Device',
        deviceType: 'Light',
        passcode: passcode,
        nodeId: nodeId,
      );
      
      if (result != null) {
        _showResult('✅ SUCCESS: Device paired successfully!\n'
                   'Name: ${result.name}\n'
                   'Node ID: ${result.nodeId}\n'
                   'Type: ${result.deviceType}');
      } else {
        _showResult('❌ FAILED: Device pairing failed.\n'
                   'Check the debug logs for details.');
      }
      
    } catch (e) {
      _showResult('❌ ERROR: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }
  
  Future<void> _testToggle() async {
    final nodeIdText = _nodeIdController.text.trim();
    
    if (nodeIdText.isEmpty) {
      _showResult('Please enter node ID');
      return;
    }
    
    final nodeId = int.tryParse(nodeIdText);
    if (nodeId == null) {
      _showResult('Invalid node ID');
      return;
    }
    
    setState(() {
      _isLoading = true;
      _lastResult = 'Testing toggle...';
    });
    
    try {
      await ApiDebugHelper.testDeviceToggle(
        nodeId: nodeId,
        endpointId: 1,
      );
      
      _showResult('Toggle test completed. Check debug logs for results.');
      
    } catch (e) {
      _showResult('❌ ERROR: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }
  
  void _showResult(String result) {
    setState(() => _lastResult = result);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(result),
        backgroundColor: result.startsWith('✅') ? Colors.green : Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Debug Device Pairing'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Debug Device Pairing',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _passcodeController,
                      decoration: const InputDecoration(
                        labelText: 'Passcode (from Home Assistant)',
                        border: OutlineInputBorder(),
                        hintText: 'Enter the passcode from "Share Device"',
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _nodeIdController,
                      decoration: const InputDecoration(
                        labelText: 'Node ID',
                        border: OutlineInputBorder(),
                        hintText: 'Enter a unique node ID (e.g., 1, 2, 3)',
                      ),
                      keyboardType: TextInputType.number,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _isLoading ? null : _testPairing,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                            ),
                            child: _isLoading
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                    ),
                                  )
                                : const Text('Test Pairing'),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _isLoading ? null : _testToggle,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.orange,
                              foregroundColor: Colors.white,
                            ),
                            child: const Text('Test Toggle'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Last Result',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: Text(
                        _lastResult.isEmpty ? 'No tests run yet' : _lastResult,
                        style: const TextStyle(fontFamily: 'monospace'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Instructions',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '1. Get a passcode from Home Assistant by clicking "Share Device"\n'
                      '2. Enter the passcode and a unique node ID\n'
                      '3. Click "Test Pairing" to see detailed debug logs\n'
                      '4. Check the Flutter console/logs for detailed output\n'
                      '5. Compare the request format with what works in Postman',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
