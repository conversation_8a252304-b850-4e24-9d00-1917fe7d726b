import 'dart:convert';
import 'dart:developer';
import 'package:dio/dio.dart';
import '../config/api_config.dart';
import '../services/matter_binding_service.dart';

/// Debug helper to test API responses and compare with Postman results
class ApiDebugHelper {
  
  /// Test device pairing with detailed logging
  static Future<void> testDevicePairing({
    required String passcode,
    required int nodeId,
  }) async {
    log('=== API DEBUG: Testing Device Pairing ===');
    log('Passcode: $passcode');
    log('Node ID: $nodeId');
    log('URL: ${ApiConfig.pairUrl}');
    
    try {
      // Prepare request exactly like the app does
      var headers = ApiConfig.headers;
      var data = jsonEncode({'node_id': nodeId, 'passcode': passcode});
      
      log('Headers: ${jsonEncode(headers)}');
      log('Payload: $data');
      
      var dio = ApiConfig.getDio();
      
      log('Making request...');
      var response = await dio.request(
        ApiConfig.pairUrl,
        options: Options(method: 'POST', headers: headers),
        data: data,
      );
      
      log('=== RAW RESPONSE ===');
      log('Status Code: ${response.statusCode}');
      log('Status Message: ${response.statusMessage}');
      log('Headers: ${response.headers}');
      log('Raw Data: ${jsonEncode(response.data)}');
      
      // Parse with ChipToolResponse
      final chipToolResponse = ChipToolResponse.fromDioResponse(
        response,
        operationType: 'commission',
      );
      
      log('=== PARSED RESPONSE ===');
      log('Success: ${chipToolResponse.success}');
      log('Return Code: ${chipToolResponse.returnCode}');
      log('Error Message: ${chipToolResponse.errorMessage}');
      log('Stdout: ${chipToolResponse.stdout}');
      log('Stderr: ${chipToolResponse.stderr}');
      
      // Analyze the response
      if (chipToolResponse.success) {
        log('✅ PAIRING SUCCESSFUL');
      } else {
        log('❌ PAIRING FAILED');
        _analyzeFailure(chipToolResponse);
      }
      
    } catch (e, stackTrace) {
      log('❌ EXCEPTION OCCURRED: $e');
      log('Stack trace: $stackTrace');
    }
    
    log('=== END API DEBUG ===');
  }
  
  /// Test device toggle with detailed logging
  static Future<void> testDeviceToggle({
    required int nodeId,
    required int endpointId,
  }) async {
    log('=== API DEBUG: Testing Device Toggle ===');
    log('Node ID: $nodeId');
    log('Endpoint ID: $endpointId');
    log('URL: ${ApiConfig.toggleUrl}');
    
    try {
      var headers = ApiConfig.headers;
      var data = jsonEncode({'node_id': nodeId, 'endpoint_id': endpointId});
      
      log('Headers: ${jsonEncode(headers)}');
      log('Payload: $data');
      
      var dio = ApiConfig.getDio();
      var response = await dio.request(
        ApiConfig.toggleUrl,
        options: Options(method: 'POST', headers: headers),
        data: data,
      );
      
      log('=== RAW RESPONSE ===');
      log('Status Code: ${response.statusCode}');
      log('Raw Data: ${jsonEncode(response.data)}');
      
      final chipToolResponse = ChipToolResponse.fromDioResponse(
        response,
        operationType: 'toggle',
      );
      
      log('=== PARSED RESPONSE ===');
      log('Success: ${chipToolResponse.success}');
      log('Return Code: ${chipToolResponse.returnCode}');
      log('Error Message: ${chipToolResponse.errorMessage}');
      log('Stdout: ${chipToolResponse.stdout}');
      log('Stderr: ${chipToolResponse.stderr}');
      
      if (chipToolResponse.success) {
        log('✅ TOGGLE SUCCESSFUL');
      } else {
        log('❌ TOGGLE FAILED');
        _analyzeFailure(chipToolResponse);
      }
      
    } catch (e, stackTrace) {
      log('❌ EXCEPTION OCCURRED: $e');
      log('Stack trace: $stackTrace');
    }
    
    log('=== END API DEBUG ===');
  }
  
  /// Analyze failure reasons
  static void _analyzeFailure(ChipToolResponse response) {
    log('=== FAILURE ANALYSIS ===');
    
    if (response.returnCode != 0) {
      log('• Non-zero return code: ${response.returnCode}');
    }
    
    if (response.stderr?.isNotEmpty == true) {
      log('• Error output present: ${response.stderr}');
      
      if (response.stderr!.contains('CHIP Error')) {
        log('  → CHIP protocol error detected');
      }
      if (response.stderr!.contains('timeout')) {
        log('  → Timeout error detected');
      }
      if (response.stderr!.contains('connection')) {
        log('  → Connection error detected');
      }
    }
    
    if (response.stdout?.isNotEmpty == true) {
      log('• Standard output: ${response.stdout}');
      
      if (!response.stdout!.contains('Shutting down')) {
        log('  → Missing expected "Shutting down" message');
      }
      if (response.stdout!.contains('Failed')) {
        log('  → Contains failure message');
      }
    }
    
    // Common failure patterns
    final commonFailures = [
      'Failed to establish secure session',
      'Device not found',
      'Invalid passcode',
      'Timeout',
      'Connection refused',
      'CHIP Error',
    ];
    
    for (final pattern in commonFailures) {
      if (response.stdout?.contains(pattern) == true || 
          response.stderr?.contains(pattern) == true) {
        log('  → Detected common failure: $pattern');
      }
    }
  }
  
  /// Compare request format with what works in Postman
  static void logPostmanComparison({
    required String passcode,
    required int nodeId,
  }) {
    log('=== POSTMAN COMPARISON ===');
    log('Flutter App Request:');
    log('URL: ${ApiConfig.pairUrl}');
    log('Method: POST');
    log('Headers: ${jsonEncode(ApiConfig.headers)}');
    log('Body: ${jsonEncode({'node_id': nodeId, 'passcode': passcode})}');
    log('');
    log('Expected Postman Request:');
    log('URL: http://*************:6000/pair');
    log('Method: POST');
    log('Headers: {"Content-Type": "application/json"}');
    log('Body: {"node_id": $nodeId, "passcode": "$passcode"}');
    log('');
    log('Check if these match exactly!');
  }
}
