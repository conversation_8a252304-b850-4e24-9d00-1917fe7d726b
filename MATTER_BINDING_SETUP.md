# Matter Binding Setup Guide

## Quick Setup

### 1. Configure IP Address

Before using the Matter binding functionality, you need to update the IP address in the configuration file:

1. Open `lib/config/api_config.dart`
2. Update the `homeAssistantIp` value to match your Home Assistant server IP:

```dart
static const String homeAssistantIp = '*************'; // Change this to your IP
```

### 2. Find Your Home Assistant IP

To find your Home Assistant IP address:
- Go to Home Assistant → Settings → System → Network
- Look for the IPv4 address
- Or use your router's admin panel to find the IP

### 3. Verify CHIPTool Service

Make sure the CHIPTool service is running on port 6000:
- Test with: `curl http://YOUR_IP:6000/`
- The service should respond (even with an error is fine, as long as it connects)

## API Endpoints

The app uses these endpoints:
- `POST /pair` - Commission devices
- `POST /bind` - Create bindings between devices  
- `POST /toggle` - Test device functionality
- `POST /command` - Clear chip-tool storage

## Payload Formats

### Pairing Device
```json
{
  "node_id": 1,
  "passcode": "12988108191"
}
```

### Creating Binding
```json
{
  "switch_node": 1,
  "switch_endpoint": 1,
  "light_node": 2,
  "light_endpoint": 1
}
```

### Testing Device
```json
{
  "node_id": 1,
  "endpoint_id": 1
}
```

## Troubleshooting

### Common Issues

1. **Connection Timeout**
   - Verify the IP address is correct
   - Check that port 6000 is accessible
   - Ensure your device is on the same network

2. **Pairing Fails**
   - Check the passcode is correct
   - Ensure the node ID is unique
   - Verify the device is in pairing mode

3. **Binding Fails**
   - Make sure both devices are paired first
   - Check that the devices support the required clusters
   - Verify endpoint IDs are correct

### Debug Logs

The app provides detailed logging. Check the Flutter console for:
- Request URLs and payloads
- Response status codes and bodies
- Error messages with context

### Testing with Postman

You can test the API directly with Postman using the examples from the working configuration:

1. **Pair Switch**: `POST http://*************:6000/pair`
2. **Pair Light**: `POST http://*************:6000/pair`  
3. **Create Binding**: `POST http://*************:6000/bind`
4. **Test Toggle**: `POST http://*************:6000/toggle`

## Configuration Changes

If you need to change the IP address after building the app:
1. Update `lib/config/api_config.dart`
2. Rebuild the app: `flutter clean && flutter build`
3. Reinstall on your device

The centralized configuration ensures all services use the same settings.
